"""
Module d'intégration avec l'IA Llama 3.3
"""

import requests
import json
import logging
from typing import Dict, List, Optional
from config import OPENROUTER_API_KEY, OPENROUTER_BASE_URL, MODEL_NAME, SYSTEM_PROMPT, TEMPERATURE, MAX_TOKENS

class AICorrector:
    """Classe pour l'intégration avec l'IA de correction"""
    
    def __init__(self):
        self.api_key = OPENROUTER_API_KEY
        self.base_url = OPENROUTER_BASE_URL
        self.model = MODEL_NAME
        self.logger = logging.getLogger(__name__)
        
        # Headers pour les requêtes API
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:3000",
            "X-Title": "Système de Correction Automatique"
        }
    
    def test_connection(self) -> bool:
        """
        Teste la connexion à l'API
        
        Returns:
            True si la connexion fonctionne, False sinon
        """
        try:
            response = self._make_request("Test de connexion")
            return response is not None
        except Exception as e:
            self.logger.error(f"Erreur de connexion à l'API: {e}")
            return False
    
    def _make_request(self, prompt: str, system_message: str = SYSTEM_PROMPT) -> Optional[str]:
        """
        Effectue une requête à l'API Llama 3.3
        
        Args:
            prompt: Le prompt à envoyer
            system_message: Message système pour contextualiser
            
        Returns:
            Réponse de l'IA ou None en cas d'erreur
        """
        try:
            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                "temperature": TEMPERATURE,
                "max_tokens": MAX_TOKENS
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                self.logger.error(f"Erreur API: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la requête API: {e}")
            return None
    
    def analyze_prototype(self, prototype_content: str) -> Dict[str, any]:
        """
        Analyse le prototype avec l'IA
        
        Args:
            prototype_content: Contenu du prototype PDF
            
        Returns:
            Analyse du prototype par l'IA
        """
        prompt = f"""
        Analysez ce prototype de correction d'examen et identifiez :
        1. Les questions principales
        2. Les réponses attendues
        3. Les critères de notation
        4. La structure de l'examen
        
        Prototype :
        {prototype_content}
        
        Répondez au format JSON avec les clés : questions, reponses_attendues, criteres_notation, structure
        """
        
        response = self._make_request(prompt)
        if response:
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                return {"raw_response": response}
        return {}
    
    def correct_student_answer(self, question: str, student_answer: str, expected_answer: str, max_points: float = 20) -> Dict[str, any]:
        """
        Corrige la réponse d'un étudiant
        
        Args:
            question: La question posée
            student_answer: Réponse de l'étudiant
            expected_answer: Réponse attendue du prototype
            max_points: Points maximum pour cette question
            
        Returns:
            Dictionnaire avec la note et les commentaires
        """
        prompt = f"""
        Corrigez cette réponse d'étudiant en vous basant sur la réponse attendue.
        
        QUESTION :
        {question}
        
        RÉPONSE ATTENDUE (PROTOTYPE) :
        {expected_answer}
        
        RÉPONSE DE L'ÉTUDIANT :
        {student_answer}
        
        CONSIGNES DE CORRECTION :
        - Note maximale : {max_points} points
        - Soyez juste et objectif
        - Accordez des points partiels si approprié
        - Justifiez votre notation
        
        Répondez au format JSON avec les clés :
        - note : (nombre entre 0 et {max_points})
        - commentaire : (justification de la note)
        - points_forts : (ce qui est correct)
        - points_faibles : (ce qui manque ou est incorrect)
        """
        
        response = self._make_request(prompt)
        if response:
            try:
                result = json.loads(response)
                # Validation de la note
                if "note" in result:
                    result["note"] = max(0, min(float(result["note"]), max_points))
                return result
            except (json.JSONDecodeError, ValueError):
                # Fallback si le JSON n'est pas valide
                return {
                    "note": 0,
                    "commentaire": "Erreur lors de l'analyse de la réponse",
                    "raw_response": response
                }
        return {
            "note": 0,
            "commentaire": "Impossible d'analyser la réponse",
            "erreur": "Pas de réponse de l'IA"
        }
    
    def generate_final_feedback(self, student_name: str, total_score: float, max_score: float, detailed_results: List[Dict]) -> str:
        """
        Génère un feedback final pour l'étudiant
        
        Args:
            student_name: Nom de l'étudiant
            total_score: Score total obtenu
            max_score: Score maximum possible
            detailed_results: Résultats détaillés par question
            
        Returns:
            Feedback personnalisé
        """
        percentage = (total_score / max_score) * 100 if max_score > 0 else 0
        
        prompt = f"""
        Générez un feedback constructif pour cet étudiant :
        
        Nom : {student_name}
        Score : {total_score}/{max_score} ({percentage:.1f}%)
        
        Résultats détaillés :
        {json.dumps(detailed_results, indent=2, ensure_ascii=False)}
        
        Le feedback doit être :
        - Encourageant et constructif
        - Spécifique aux performances de l'étudiant
        - Avec des suggestions d'amélioration
        - Professionnel et bienveillant
        """
        
        response = self._make_request(prompt)
        return response if response else f"Score: {total_score}/{max_score} ({percentage:.1f}%)"
