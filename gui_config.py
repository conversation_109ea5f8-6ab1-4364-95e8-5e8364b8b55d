"""
Interface de configuration avancée pour le système de correction
"""

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
import json
import os
from config import *

class ConfigWindow:
    """Fenêtre de configuration avancée"""
    
    def __init__(self, parent):
        self.parent = parent
        self.window = tk.Toplevel(parent)
        self.window.title("Configuration Avancée")
        self.window.geometry("600x500")
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variables de configuration
        self.setup_variables()
        self.setup_ui()
        self.load_config()
        
        # Centrer la fenêtre
        self.center_window()
    
    def setup_variables(self):
        """Initialise les variables de configuration"""
        self.api_key_var = tk.StringVar(value=OPENROUTER_API_KEY)
        self.model_var = tk.StringVar(value=MODEL_NAME)
        self.max_score_var = tk.StringVar(value=str(MAX_SCORE))
        self.temperature_var = tk.StringVar(value=str(TEMPERATURE))
        self.max_tokens_var = tk.StringVar(value=str(MAX_TOKENS))
        
        # Chemins par défaut
        self.prototype_default_var = tk.StringVar(value=PROTOTYPE_PDF_PATH)
        self.students_default_var = tk.StringVar(value=STUDENTS_FILE_PATH)
        self.exam_default_var = tk.StringVar(value=EXAM_PDF_PATH)
        self.results_default_var = tk.StringVar(value=RESULTS_FILE_PATH)
        
        # Options avancées
        self.auto_save_var = tk.BooleanVar(value=True)
        self.backup_var = tk.BooleanVar(value=True)
        self.detailed_logs_var = tk.BooleanVar(value=True)
        self.theme_var = tk.StringVar(value="clam")
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Notebook pour les onglets
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Onglet IA
        self.create_ai_tab(notebook)
        
        # Onglet Fichiers
        self.create_files_tab(notebook)
        
        # Onglet Interface
        self.create_interface_tab(notebook)
        
        # Onglet Avancé
        self.create_advanced_tab(notebook)
        
        # Boutons de contrôle
        self.create_control_buttons()
    
    def create_ai_tab(self, notebook):
        """Crée l'onglet de configuration IA"""
        ai_frame = ttk.Frame(notebook)
        notebook.add(ai_frame, text="🤖 Intelligence Artificielle")
        
        # Frame principal avec padding
        main_frame = ttk.Frame(ai_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Configuration API
        api_group = ttk.LabelFrame(main_frame, text="Configuration API", padding="10")
        api_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(api_group, text="Clé API OpenRouter:").grid(row=0, column=0, sticky=tk.W, pady=2)
        api_entry = ttk.Entry(api_group, textvariable=self.api_key_var, width=50, show="*")
        api_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        api_group.columnconfigure(1, weight=1)
        
        ttk.Button(api_group, text="👁️", command=self.toggle_api_visibility).grid(row=0, column=2, padx=(5, 0))
        
        ttk.Label(api_group, text="Modèle:").grid(row=1, column=0, sticky=tk.W, pady=2)
        model_combo = ttk.Combobox(api_group, textvariable=self.model_var, width=47)
        model_combo['values'] = [
            "meta-llama/llama-3.3-70b-instruct:free",
            "meta-llama/llama-3.1-8b-instruct:free",
            "microsoft/phi-3-mini-128k-instruct:free",
            "google/gemma-2-9b-it:free"
        ]
        model_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        
        # Paramètres IA
        params_group = ttk.LabelFrame(main_frame, text="Paramètres de génération", padding="10")
        params_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(params_group, text="Température (0-1):").grid(row=0, column=0, sticky=tk.W, pady=2)
        temp_scale = ttk.Scale(params_group, from_=0, to=1, variable=self.temperature_var, orient=tk.HORIZONTAL)
        temp_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        temp_label = ttk.Label(params_group, textvariable=self.temperature_var)
        temp_label.grid(row=0, column=2, padx=(5, 0))
        params_group.columnconfigure(1, weight=1)
        
        ttk.Label(params_group, text="Tokens max:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(params_group, textvariable=self.max_tokens_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Test de connexion
        test_frame = ttk.Frame(main_frame)
        test_frame.pack(fill=tk.X, pady=10)
        
        self.test_button = ttk.Button(test_frame, text="🧪 Tester la connexion", command=self.test_connection)
        self.test_button.pack(side=tk.LEFT)
        
        self.connection_label = ttk.Label(test_frame, text="Non testée", foreground="orange")
        self.connection_label.pack(side=tk.LEFT, padx=(10, 0))
    
    def create_files_tab(self, notebook):
        """Crée l'onglet de configuration des fichiers"""
        files_frame = ttk.Frame(notebook)
        notebook.add(files_frame, text="📁 Fichiers")
        
        main_frame = ttk.Frame(files_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Chemins par défaut
        paths_group = ttk.LabelFrame(main_frame, text="Chemins par défaut", padding="10")
        paths_group.pack(fill=tk.X, pady=(0, 10))
        paths_group.columnconfigure(1, weight=1)
        
        files_config = [
            ("Prototype PDF:", self.prototype_default_var),
            ("Fichier élèves:", self.students_default_var),
            ("Examen PDF:", self.exam_default_var),
            ("Résultats:", self.results_default_var)
        ]
        
        for i, (label, var) in enumerate(files_config):
            ttk.Label(paths_group, text=label).grid(row=i, column=0, sticky=tk.W, pady=2)
            ttk.Entry(paths_group, textvariable=var, width=40).grid(row=i, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
            ttk.Button(paths_group, text="📁", command=lambda v=var: self.browse_default_file(v)).grid(row=i, column=2, pady=2)
        
        # Options de fichiers
        file_options_group = ttk.LabelFrame(main_frame, text="Options", padding="10")
        file_options_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(file_options_group, text="Sauvegarde automatique", variable=self.auto_save_var).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(file_options_group, text="Créer des sauvegardes", variable=self.backup_var).pack(anchor=tk.W, pady=2)
    
    def create_interface_tab(self, notebook):
        """Crée l'onglet de configuration de l'interface"""
        interface_frame = ttk.Frame(notebook)
        notebook.add(interface_frame, text="🎨 Interface")
        
        main_frame = ttk.Frame(interface_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Thème
        theme_group = ttk.LabelFrame(main_frame, text="Apparence", padding="10")
        theme_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(theme_group, text="Thème:").grid(row=0, column=0, sticky=tk.W, pady=2)
        theme_combo = ttk.Combobox(theme_group, textvariable=self.theme_var)
        theme_combo['values'] = ["clam", "alt", "default", "classic"]
        theme_combo.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        ttk.Button(theme_group, text="Appliquer", command=self.apply_theme).grid(row=0, column=2, padx=(5, 0))
        
        # Options d'affichage
        display_group = ttk.LabelFrame(main_frame, text="Affichage", padding="10")
        display_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(display_group, text="Logs détaillés", variable=self.detailed_logs_var).pack(anchor=tk.W, pady=2)
        
        # Prévisualisation
        preview_group = ttk.LabelFrame(main_frame, text="Prévisualisation", padding="10")
        preview_group.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        preview_text = tk.Text(preview_group, height=8, wrap=tk.WORD)
        preview_text.pack(fill=tk.BOTH, expand=True)
        preview_text.insert(tk.END, "Ceci est un aperçu de l'interface avec le thème sélectionné.\n\n")
        preview_text.insert(tk.END, "✅ Connexion IA réussie\n")
        preview_text.insert(tk.END, "📁 Fichiers chargés\n")
        preview_text.insert(tk.END, "🚀 Correction en cours...\n")
        preview_text.insert(tk.END, "📊 Résultats générés\n")
        preview_text.config(state=tk.DISABLED)
    
    def create_advanced_tab(self, notebook):
        """Crée l'onglet de configuration avancée"""
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text="⚙️ Avancé")
        
        main_frame = ttk.Frame(advanced_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Paramètres de correction
        correction_group = ttk.LabelFrame(main_frame, text="Paramètres de correction", padding="10")
        correction_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(correction_group, text="Note maximale:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(correction_group, textvariable=self.max_score_var, width=10).grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Prompt système personnalisé
        prompt_group = ttk.LabelFrame(main_frame, text="Prompt système", padding="10")
        prompt_group.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.prompt_text = tk.Text(prompt_group, height=8, wrap=tk.WORD)
        self.prompt_text.pack(fill=tk.BOTH, expand=True)
        self.prompt_text.insert(tk.END, SYSTEM_PROMPT)
        
        # Boutons de gestion
        prompt_buttons = ttk.Frame(prompt_group)
        prompt_buttons.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(prompt_buttons, text="Réinitialiser", command=self.reset_prompt).pack(side=tk.LEFT)
        ttk.Button(prompt_buttons, text="Sauvegarder", command=self.save_prompt).pack(side=tk.LEFT, padx=(5, 0))
    
    def create_control_buttons(self):
        """Crée les boutons de contrôle"""
        button_frame = ttk.Frame(self.window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(button_frame, text="💾 Sauvegarder", command=self.save_config).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="❌ Annuler", command=self.cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="🔄 Réinitialiser", command=self.reset_config).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="📤 Exporter", command=self.export_config).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(button_frame, text="📥 Importer", command=self.import_config).pack(side=tk.LEFT, padx=(5, 0))
    
    def toggle_api_visibility(self):
        """Bascule la visibilité de la clé API"""
        # Cette fonction nécessiterait une référence à l'Entry widget
        pass
    
    def test_connection(self):
        """Teste la connexion avec les paramètres actuels"""
        self.connection_label.config(text="Test en cours...", foreground="orange")
        self.test_button.config(state=tk.DISABLED)
        
        def test_thread():
            try:
                # Simulation du test (à remplacer par le vrai test)
                import time
                time.sleep(2)
                
                # Mise à jour de l'interface
                self.window.after(0, lambda: self.connection_label.config(text="✅ Connecté", foreground="green"))
                self.window.after(0, lambda: self.test_button.config(state=tk.NORMAL))
                
            except Exception as e:
                self.window.after(0, lambda: self.connection_label.config(text="❌ Échec", foreground="red"))
                self.window.after(0, lambda: self.test_button.config(state=tk.NORMAL))
        
        import threading
        threading.Thread(target=test_thread, daemon=True).start()
    
    def browse_default_file(self, var):
        """Parcourt pour sélectionner un fichier par défaut"""
        from tkinter import filedialog
        
        filename = filedialog.askopenfilename(
            title="Sélectionner un fichier par défaut",
            filetypes=[("Tous les fichiers", "*.*")]
        )
        
        if filename:
            var.set(filename)
    
    def apply_theme(self):
        """Applique le thème sélectionné"""
        try:
            style = ttk.Style()
            style.theme_use(self.theme_var.get())
            messagebox.showinfo("Thème", f"Thème '{self.theme_var.get()}' appliqué!")
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible d'appliquer le thème: {e}")
    
    def reset_prompt(self):
        """Remet le prompt par défaut"""
        self.prompt_text.delete(1.0, tk.END)
        self.prompt_text.insert(tk.END, SYSTEM_PROMPT)
    
    def save_prompt(self):
        """Sauvegarde le prompt personnalisé"""
        prompt = self.prompt_text.get(1.0, tk.END).strip()
        # Ici, on sauvegarderait le prompt dans la configuration
        messagebox.showinfo("Sauvegarde", "Prompt système sauvegardé!")
    
    def save_config(self):
        """Sauvegarde la configuration"""
        config = {
            "api_key": self.api_key_var.get(),
            "model": self.model_var.get(),
            "max_score": float(self.max_score_var.get()),
            "temperature": float(self.temperature_var.get()),
            "max_tokens": int(self.max_tokens_var.get()),
            "prototype_default": self.prototype_default_var.get(),
            "students_default": self.students_default_var.get(),
            "exam_default": self.exam_default_var.get(),
            "results_default": self.results_default_var.get(),
            "auto_save": self.auto_save_var.get(),
            "backup": self.backup_var.get(),
            "detailed_logs": self.detailed_logs_var.get(),
            "theme": self.theme_var.get(),
            "system_prompt": self.prompt_text.get(1.0, tk.END).strip()
        }
        
        try:
            with open("user_config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            messagebox.showinfo("Succès", "Configuration sauvegardée avec succès!")
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")
    
    def load_config(self):
        """Charge la configuration depuis le fichier"""
        try:
            if os.path.exists("user_config.json"):
                with open("user_config.json", "r", encoding="utf-8") as f:
                    config = json.load(f)
                
                # Charger les valeurs
                self.api_key_var.set(config.get("api_key", OPENROUTER_API_KEY))
                self.model_var.set(config.get("model", MODEL_NAME))
                self.max_score_var.set(str(config.get("max_score", MAX_SCORE)))
                self.temperature_var.set(str(config.get("temperature", TEMPERATURE)))
                self.max_tokens_var.set(str(config.get("max_tokens", MAX_TOKENS)))
                
                self.prototype_default_var.set(config.get("prototype_default", PROTOTYPE_PDF_PATH))
                self.students_default_var.set(config.get("students_default", STUDENTS_FILE_PATH))
                self.exam_default_var.set(config.get("exam_default", EXAM_PDF_PATH))
                self.results_default_var.set(config.get("results_default", RESULTS_FILE_PATH))
                
                self.auto_save_var.set(config.get("auto_save", True))
                self.backup_var.set(config.get("backup", True))
                self.detailed_logs_var.set(config.get("detailed_logs", True))
                self.theme_var.set(config.get("theme", "clam"))
                
                if "system_prompt" in config:
                    self.prompt_text.delete(1.0, tk.END)
                    self.prompt_text.insert(tk.END, config["system_prompt"])
                    
        except Exception as e:
            messagebox.showwarning("Avertissement", f"Erreur lors du chargement de la configuration: {e}")
    
    def reset_config(self):
        """Remet la configuration par défaut"""
        if messagebox.askyesno("Confirmation", "Voulez-vous vraiment remettre la configuration par défaut?"):
            self.setup_variables()
            self.reset_prompt()
    
    def export_config(self):
        """Exporte la configuration"""
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            title="Exporter la configuration",
            defaultextension=".json",
            filetypes=[("Fichiers JSON", "*.json")]
        )
        
        if filename:
            # Code d'export ici
            messagebox.showinfo("Export", f"Configuration exportée vers {filename}")
    
    def import_config(self):
        """Importe une configuration"""
        from tkinter import filedialog
        
        filename = filedialog.askopenfilename(
            title="Importer une configuration",
            filetypes=[("Fichiers JSON", "*.json")]
        )
        
        if filename:
            # Code d'import ici
            messagebox.showinfo("Import", f"Configuration importée depuis {filename}")
    
    def cancel(self):
        """Annule et ferme la fenêtre"""
        self.window.destroy()
    
    def center_window(self):
        """Centre la fenêtre sur l'écran"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")

def show_config_window(parent):
    """Affiche la fenêtre de configuration"""
    ConfigWindow(parent)
