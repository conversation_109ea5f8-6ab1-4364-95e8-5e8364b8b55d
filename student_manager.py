"""
Module de gestion des fichiers élèves
"""

import logging
from typing import List, Dict, Optional
import re

class StudentManager:
    """Classe pour gérer les informations des élèves"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.students = []
    
    def load_students_from_file(self, file_path: str) -> List[Dict[str, str]]:
        """
        Charge la liste des élèves depuis un fichier texte
        
        Args:
            file_path: Chemin vers le fichier des élèves
            
        Returns:
            Liste des élèves avec leurs informations
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            self.students = self._parse_students_content(content)
            self.logger.info(f"Chargé {len(self.students)} élèves depuis {file_path}")
            return self.students
            
        except FileNotFoundError:
            self.logger.error(f"Fichier élèves non trouvé: {file_path}")
            return []
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des élèves: {e}")
            return []
    
    def _parse_students_content(self, content: str) -> List[Dict[str, str]]:
        """
        Parse le contenu du fichier élèves
        
        Args:
            content: Contenu du fichier
            
        Returns:
            Liste des élèves parsés
        """
        students = []
        lines = content.strip().split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):  # Ignorer les lignes vides et commentaires
                continue
            
            student = self._parse_student_line(line, line_num)
            if student:
                students.append(student)
        
        return students
    
    def _parse_student_line(self, line: str, line_num: int) -> Optional[Dict[str, str]]:
        """
        Parse une ligne contenant les informations d'un élève
        
        Formats supportés :
        - "Nom Prénom"
        - "Nom, Prénom"
        - "ID:123 Nom Prénom"
        - "Nom Prénom - Classe"
        
        Args:
            line: Ligne à parser
            line_num: Numéro de ligne pour le debug
            
        Returns:
            Dictionnaire avec les informations de l'élève ou None
        """
        try:
            # Pattern pour ID:123 Nom Prénom
            id_pattern = r'^ID:(\d+)\s+(.+)$'
            id_match = re.match(id_pattern, line)
            
            if id_match:
                student_id = id_match.group(1)
                name_part = id_match.group(2)
            else:
                student_id = str(line_num)
                name_part = line
            
            # Séparer nom et classe si présent
            if ' - ' in name_part:
                name_part, classe = name_part.split(' - ', 1)
            else:
                classe = ""
            
            # Parser le nom et prénom
            if ',' in name_part:
                # Format "Nom, Prénom"
                parts = name_part.split(',', 1)
                nom = parts[0].strip()
                prenom = parts[1].strip() if len(parts) > 1 else ""
            else:
                # Format "Nom Prénom" ou "Prénom Nom"
                parts = name_part.strip().split()
                if len(parts) >= 2:
                    nom = parts[-1]  # Dernier mot = nom de famille
                    prenom = ' '.join(parts[:-1])  # Reste = prénom(s)
                elif len(parts) == 1:
                    nom = parts[0]
                    prenom = ""
                else:
                    return None
            
            return {
                'id': student_id,
                'nom': nom,
                'prenom': prenom,
                'nom_complet': f"{prenom} {nom}".strip(),
                'classe': classe,
                'ligne_originale': line
            }
            
        except Exception as e:
            self.logger.warning(f"Erreur lors du parsing de la ligne {line_num}: '{line}' - {e}")
            return None
    
    def get_student_by_id(self, student_id: str) -> Optional[Dict[str, str]]:
        """
        Récupère un élève par son ID
        
        Args:
            student_id: ID de l'élève
            
        Returns:
            Informations de l'élève ou None
        """
        for student in self.students:
            if student['id'] == student_id:
                return student
        return None
    
    def get_student_by_name(self, name: str) -> Optional[Dict[str, str]]:
        """
        Récupère un élève par son nom
        
        Args:
            name: Nom complet ou partiel de l'élève
            
        Returns:
            Informations de l'élève ou None
        """
        name_lower = name.lower()
        
        for student in self.students:
            if (name_lower in student['nom_complet'].lower() or
                name_lower in student['nom'].lower() or
                name_lower in student['prenom'].lower()):
                return student
        return None
    
    def get_all_students(self) -> List[Dict[str, str]]:
        """
        Retourne la liste de tous les élèves
        
        Returns:
            Liste complète des élèves
        """
        return self.students.copy()
    
    def validate_students_data(self) -> Dict[str, any]:
        """
        Valide les données des élèves chargées
        
        Returns:
            Rapport de validation
        """
        total = len(self.students)
        valid = 0
        duplicates = []
        missing_info = []
        
        seen_ids = set()
        seen_names = set()
        
        for student in self.students:
            is_valid = True
            
            # Vérifier les doublons d'ID
            if student['id'] in seen_ids:
                duplicates.append(f"ID dupliqué: {student['id']}")
                is_valid = False
            seen_ids.add(student['id'])
            
            # Vérifier les doublons de noms
            name_key = student['nom_complet'].lower()
            if name_key in seen_names:
                duplicates.append(f"Nom dupliqué: {student['nom_complet']}")
                is_valid = False
            seen_names.add(name_key)
            
            # Vérifier les informations manquantes
            if not student['nom'] or not student['prenom']:
                missing_info.append(f"Informations incomplètes: {student['ligne_originale']}")
                is_valid = False
            
            if is_valid:
                valid += 1
        
        return {
            'total': total,
            'valid': valid,
            'invalid': total - valid,
            'duplicates': duplicates,
            'missing_info': missing_info,
            'success_rate': (valid / total * 100) if total > 0 else 0
        }
