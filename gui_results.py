"""
Interface de visualisation des résultats de correction
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib
matplotlib.use('TkAgg')

class ResultsViewer:
    """Visualiseur de résultats de correction"""
    
    def __init__(self, parent, results_file=None):
        self.parent = parent
        self.window = tk.Toplevel(parent)
        self.window.title("📊 Visualisation des Résultats")
        self.window.geometry("1000x700")
        self.window.transient(parent)
        
        self.results_data = None
        self.results_file = results_file
        
        self.setup_ui()
        
        if results_file and os.path.exists(results_file):
            self.load_results(results_file)
        
        self.center_window()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Menu
        self.create_menu()
        
        # Frame principal
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Notebook pour les onglets
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Onglet Résumé
        self.create_summary_tab()
        
        # Onglet Détails
        self.create_details_tab()
        
        # Onglet Graphiques
        self.create_charts_tab()
        
        # Onglet Statistiques
        self.create_stats_tab()
        
        # Barre de statut
        self.status_var = tk.StringVar(value="Aucun fichier chargé")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(5, 0))
    
    def create_menu(self):
        """Crée le menu de la fenêtre"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)
        
        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Fichier", menu=file_menu)
        file_menu.add_command(label="Ouvrir résultats...", command=self.open_results_file)
        file_menu.add_command(label="Actualiser", command=self.refresh_data)
        file_menu.add_separator()
        file_menu.add_command(label="Exporter PDF", command=self.export_pdf)
        file_menu.add_command(label="Exporter CSV", command=self.export_csv)
        file_menu.add_separator()
        file_menu.add_command(label="Fermer", command=self.window.destroy)
        
        # Menu Affichage
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Affichage", menu=view_menu)
        view_menu.add_command(label="Actualiser graphiques", command=self.refresh_charts)
        view_menu.add_command(label="Plein écran", command=self.toggle_fullscreen)
    
    def create_summary_tab(self):
        """Crée l'onglet de résumé"""
        summary_frame = ttk.Frame(self.notebook)
        self.notebook.add(summary_frame, text="📋 Résumé")
        
        # Frame avec scrollbar
        canvas = tk.Canvas(summary_frame)
        scrollbar = ttk.Scrollbar(summary_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Titre
        title_label = ttk.Label(scrollable_frame, text="📊 Résumé des Corrections", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # Statistiques générales
        self.stats_frame = ttk.LabelFrame(scrollable_frame, text="Statistiques Générales", padding="10")
        self.stats_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Répartition des mentions
        self.mentions_frame = ttk.LabelFrame(scrollable_frame, text="Répartition des Mentions", padding="10")
        self.mentions_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Top/Bottom performers
        self.performers_frame = ttk.LabelFrame(scrollable_frame, text="Performances", padding="10")
        self.performers_frame.pack(fill=tk.X, padx=20, pady=10)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_details_tab(self):
        """Crée l'onglet des détails"""
        details_frame = ttk.Frame(self.notebook)
        self.notebook.add(details_frame, text="📝 Détails")
        
        # Barre d'outils
        toolbar = ttk.Frame(details_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(toolbar, text="Rechercher:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(toolbar, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        search_entry.bind('<KeyRelease>', self.filter_results)
        
        ttk.Button(toolbar, text="🔍 Rechercher", command=self.search_student).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar, text="📤 Exporter sélection", command=self.export_selection).pack(side=tk.RIGHT)
        
        # Treeview pour les résultats détaillés
        columns = ("ID", "Nom", "Prénom", "Classe", "Score", "Note/20", "Mention")
        self.tree = ttk.Treeview(details_frame, columns=columns, show="headings", height=15)
        
        # Configuration des colonnes
        for col in columns:
            self.tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.tree.column(col, width=100, anchor=tk.CENTER)
        
        # Scrollbars pour le treeview
        tree_scroll_y = ttk.Scrollbar(details_frame, orient="vertical", command=self.tree.yview)
        tree_scroll_x = ttk.Scrollbar(details_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=tree_scroll_y.set, xscrollcommand=tree_scroll_x.set)
        
        # Placement
        self.tree.pack(side="left", fill="both", expand=True, padx=(5, 0), pady=5)
        tree_scroll_y.pack(side="right", fill="y", pady=5)
        tree_scroll_x.pack(side="bottom", fill="x", padx=5)
        
        # Bind pour double-clic
        self.tree.bind("<Double-1>", self.show_student_detail)
    
    def create_charts_tab(self):
        """Crée l'onglet des graphiques"""
        charts_frame = ttk.Frame(self.notebook)
        self.notebook.add(charts_frame, text="📈 Graphiques")
        
        # Frame pour les contrôles
        controls_frame = ttk.Frame(charts_frame)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(controls_frame, text="Type de graphique:").pack(side=tk.LEFT)
        self.chart_type_var = tk.StringVar(value="histogram")
        chart_combo = ttk.Combobox(controls_frame, textvariable=self.chart_type_var, width=15)
        chart_combo['values'] = ["histogram", "pie", "bar", "box"]
        chart_combo.pack(side=tk.LEFT, padx=(5, 10))
        chart_combo.bind('<<ComboboxSelected>>', self.update_chart)
        
        ttk.Button(controls_frame, text="🔄 Actualiser", command=self.refresh_charts).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="💾 Sauvegarder", command=self.save_chart).pack(side=tk.RIGHT)
        
        # Frame pour le graphique
        self.chart_frame = ttk.Frame(charts_frame)
        self.chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_stats_tab(self):
        """Crée l'onglet des statistiques avancées"""
        stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(stats_frame, text="📊 Statistiques")
        
        # Notebook pour sous-onglets
        stats_notebook = ttk.Notebook(stats_frame)
        stats_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Onglet Statistiques descriptives
        desc_frame = ttk.Frame(stats_notebook)
        stats_notebook.add(desc_frame, text="Descriptives")
        
        self.desc_text = tk.Text(desc_frame, wrap=tk.WORD, font=('Courier', 10))
        desc_scroll = ttk.Scrollbar(desc_frame, orient="vertical", command=self.desc_text.yview)
        self.desc_text.configure(yscrollcommand=desc_scroll.set)
        
        self.desc_text.pack(side="left", fill="both", expand=True)
        desc_scroll.pack(side="right", fill="y")
        
        # Onglet Comparaisons
        comp_frame = ttk.Frame(stats_notebook)
        stats_notebook.add(comp_frame, text="Comparaisons")
        
        # Onglet Tendances
        trend_frame = ttk.Frame(stats_notebook)
        stats_notebook.add(trend_frame, text="Tendances")
    
    def load_results(self, file_path):
        """Charge les résultats depuis un fichier"""
        try:
            if file_path.endswith('.json'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.results_data = json.load(f)
            elif file_path.endswith('.xlsx'):
                # Charger depuis Excel
                df = pd.read_excel(file_path, sheet_name='Résultats')
                self.results_data = {
                    'results': df.to_dict('records'),
                    'summary': self.calculate_summary_from_df(df)
                }
            
            self.results_file = file_path
            self.update_all_displays()
            self.status_var.set(f"Fichier chargé: {os.path.basename(file_path)}")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible de charger le fichier:\n{e}")
    
    def calculate_summary_from_df(self, df):
        """Calcule un résumé à partir d'un DataFrame"""
        if df.empty:
            return {}
        
        return {
            'total_students': len(df),
            'average_score': df['Score Total'].mean() if 'Score Total' in df.columns else 0,
            'min_score': df['Score Total'].min() if 'Score Total' in df.columns else 0,
            'max_score': df['Score Total'].max() if 'Score Total' in df.columns else 0,
            'pass_rate': len(df[df['Pourcentage'].str.rstrip('%').astype(float) >= 50]) / len(df) * 100 if 'Pourcentage' in df.columns else 0
        }
    
    def update_all_displays(self):
        """Met à jour tous les affichages"""
        if not self.results_data:
            return
        
        self.update_summary()
        self.update_details_tree()
        self.update_chart()
        self.update_statistics()
    
    def update_summary(self):
        """Met à jour l'onglet résumé"""
        if not self.results_data:
            return
        
        # Effacer les widgets existants
        for widget in self.stats_frame.winfo_children():
            widget.destroy()
        
        summary = self.results_data.get('summary', {})
        
        # Statistiques générales
        stats_text = f"""
        📊 Nombre d'étudiants: {summary.get('total_students', 0)}
        📈 Score moyen: {summary.get('average_score', 0):.2f}
        📉 Score minimum: {summary.get('min_score', 0):.2f}
        📈 Score maximum: {summary.get('max_score', 0):.2f}
        ✅ Taux de réussite: {summary.get('pass_rate', 0):.1f}%
        """
        
        ttk.Label(self.stats_frame, text=stats_text, font=('Courier', 10)).pack(anchor=tk.W)
    
    def update_details_tree(self):
        """Met à jour le treeview des détails"""
        # Effacer les éléments existants
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not self.results_data or 'results' not in self.results_data:
            return
        
        # Ajouter les résultats
        for result in self.results_data['results']:
            values = (
                result.get('id_etudiant', ''),
                result.get('nom', ''),
                result.get('prenom', ''),
                result.get('classe', ''),
                f"{result.get('score_total', 0):.1f}",
                f"{result.get('note_sur_20', 0):.1f}",
                result.get('mention', '')
            )
            self.tree.insert("", tk.END, values=values)
    
    def update_chart(self, event=None):
        """Met à jour le graphique"""
        if not self.results_data or 'results' not in self.results_data:
            return
        
        # Effacer le graphique existant
        for widget in self.chart_frame.winfo_children():
            widget.destroy()
        
        try:
            # Créer le graphique selon le type sélectionné
            fig, ax = plt.subplots(figsize=(8, 6))
            
            scores = [r.get('note_sur_20', 0) for r in self.results_data['results']]
            
            chart_type = self.chart_type_var.get()
            
            if chart_type == "histogram":
                ax.hist(scores, bins=10, edgecolor='black', alpha=0.7)
                ax.set_xlabel('Notes /20')
                ax.set_ylabel('Nombre d\'étudiants')
                ax.set_title('Distribution des Notes')
                
            elif chart_type == "pie":
                mentions = [r.get('mention', '') for r in self.results_data['results']]
                mention_counts = {}
                for mention in mentions:
                    mention_counts[mention] = mention_counts.get(mention, 0) + 1
                
                ax.pie(mention_counts.values(), labels=mention_counts.keys(), autopct='%1.1f%%')
                ax.set_title('Répartition des Mentions')
                
            elif chart_type == "bar":
                classes = [r.get('classe', '') for r in self.results_data['results']]
                class_scores = {}
                for i, classe in enumerate(classes):
                    if classe not in class_scores:
                        class_scores[classe] = []
                    class_scores[classe].append(scores[i])
                
                class_means = {k: sum(v)/len(v) for k, v in class_scores.items()}
                ax.bar(class_means.keys(), class_means.values())
                ax.set_xlabel('Classes')
                ax.set_ylabel('Note moyenne /20')
                ax.set_title('Notes Moyennes par Classe')
                
            elif chart_type == "box":
                ax.boxplot(scores)
                ax.set_ylabel('Notes /20')
                ax.set_title('Boîte à Moustaches des Notes')
            
            # Intégrer le graphique dans tkinter
            canvas = FigureCanvasTkAgg(fig, self.chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
        except Exception as e:
            ttk.Label(self.chart_frame, text=f"Erreur lors de la création du graphique: {e}").pack()
    
    def update_statistics(self):
        """Met à jour les statistiques avancées"""
        if not self.results_data or 'results' not in self.results_data:
            return
        
        scores = [r.get('note_sur_20', 0) for r in self.results_data['results']]
        
        if not scores:
            return
        
        # Calculer les statistiques
        import statistics
        
        stats_text = f"""
STATISTIQUES DESCRIPTIVES
========================

Nombre d'observations: {len(scores)}
Moyenne: {statistics.mean(scores):.2f}
Médiane: {statistics.median(scores):.2f}
Mode: {statistics.mode(scores) if len(set(scores)) < len(scores) else 'N/A'}
Écart-type: {statistics.stdev(scores) if len(scores) > 1 else 0:.2f}
Variance: {statistics.variance(scores) if len(scores) > 1 else 0:.2f}

Minimum: {min(scores):.2f}
Maximum: {max(scores):.2f}
Étendue: {max(scores) - min(scores):.2f}

Quartiles:
Q1 (25%): {statistics.quantiles(scores, n=4)[0]:.2f}
Q2 (50%): {statistics.median(scores):.2f}
Q3 (75%): {statistics.quantiles(scores, n=4)[2]:.2f}

Répartition par mention:
"""
        
        # Ajouter la répartition des mentions
        mentions = [r.get('mention', '') for r in self.results_data['results']]
        mention_counts = {}
        for mention in mentions:
            mention_counts[mention] = mention_counts.get(mention, 0) + 1
        
        for mention, count in mention_counts.items():
            percentage = (count / len(mentions)) * 100
            stats_text += f"{mention}: {count} ({percentage:.1f}%)\n"
        
        self.desc_text.delete(1.0, tk.END)
        self.desc_text.insert(tk.END, stats_text)
    
    def open_results_file(self):
        """Ouvre un fichier de résultats"""
        from tkinter import filedialog
        
        file_path = filedialog.askopenfilename(
            title="Ouvrir un fichier de résultats",
            filetypes=[
                ("Fichiers JSON", "*.json"),
                ("Fichiers Excel", "*.xlsx"),
                ("Tous les fichiers", "*.*")
            ]
        )
        
        if file_path:
            self.load_results(file_path)
    
    def refresh_data(self):
        """Actualise les données"""
        if self.results_file and os.path.exists(self.results_file):
            self.load_results(self.results_file)
        else:
            messagebox.showwarning("Avertissement", "Aucun fichier de résultats chargé")
    
    def refresh_charts(self):
        """Actualise les graphiques"""
        self.update_chart()
    
    def filter_results(self, event=None):
        """Filtre les résultats selon la recherche"""
        search_term = self.search_var.get().lower()
        
        # Effacer le treeview
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not self.results_data or 'results' not in self.results_data:
            return
        
        # Filtrer et afficher
        for result in self.results_data['results']:
            if (search_term in result.get('nom', '').lower() or
                search_term in result.get('prenom', '').lower() or
                search_term in result.get('classe', '').lower()):
                
                values = (
                    result.get('id_etudiant', ''),
                    result.get('nom', ''),
                    result.get('prenom', ''),
                    result.get('classe', ''),
                    f"{result.get('score_total', 0):.1f}",
                    f"{result.get('note_sur_20', 0):.1f}",
                    result.get('mention', '')
                )
                self.tree.insert("", tk.END, values=values)
    
    def search_student(self):
        """Recherche un étudiant spécifique"""
        self.filter_results()
    
    def sort_by_column(self, column):
        """Trie par colonne"""
        # Implémentation du tri
        pass
    
    def show_student_detail(self, event):
        """Affiche les détails d'un étudiant"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            student_id = item['values'][0]
            
            # Trouver l'étudiant dans les données
            student_data = None
            for result in self.results_data.get('results', []):
                if result.get('id_etudiant') == student_id:
                    student_data = result
                    break
            
            if student_data:
                self.show_student_detail_window(student_data)
    
    def show_student_detail_window(self, student_data):
        """Affiche une fenêtre avec les détails de l'étudiant"""
        detail_window = tk.Toplevel(self.window)
        detail_window.title(f"Détails - {student_data.get('nom_complet', 'Étudiant')}")
        detail_window.geometry("600x400")
        
        # Contenu des détails
        detail_text = tk.Text(detail_window, wrap=tk.WORD, font=('Courier', 10))
        detail_scroll = ttk.Scrollbar(detail_window, orient="vertical", command=detail_text.yview)
        detail_text.configure(yscrollcommand=detail_scroll.set)
        
        # Formater les détails
        details = f"""
DÉTAILS DE L'ÉTUDIANT
====================

Nom complet: {student_data.get('nom_complet', 'N/A')}
ID: {student_data.get('id_etudiant', 'N/A')}
Classe: {student_data.get('classe', 'N/A')}

RÉSULTATS
=========

Score total: {student_data.get('score_total', 0):.2f}
Score maximum: {student_data.get('score_maximum', 0):.2f}
Pourcentage: {student_data.get('pourcentage', 0):.1f}%
Note sur 20: {student_data.get('note_sur_20', 0):.2f}
Mention: {student_data.get('mention', 'N/A')}

DÉTAILS PAR QUESTION
===================

"""
        
        for i, detail in enumerate(student_data.get('details_correction', []), 1):
            details += f"Question {i}:\n"
            details += f"  Note: {detail.get('note', 0):.2f}\n"
            details += f"  Commentaire: {detail.get('commentaire', 'N/A')}\n"
            details += f"  Points forts: {detail.get('points_forts', 'N/A')}\n"
            details += f"  Points faibles: {detail.get('points_faibles', 'N/A')}\n\n"
        
        detail_text.insert(tk.END, details)
        detail_text.config(state=tk.DISABLED)
        
        detail_text.pack(side="left", fill="both", expand=True)
        detail_scroll.pack(side="right", fill="y")
    
    def export_selection(self):
        """Exporte la sélection actuelle"""
        messagebox.showinfo("Export", "Fonctionnalité d'export en développement")
    
    def export_pdf(self):
        """Exporte en PDF"""
        messagebox.showinfo("Export PDF", "Fonctionnalité d'export PDF en développement")
    
    def export_csv(self):
        """Exporte en CSV"""
        messagebox.showinfo("Export CSV", "Fonctionnalité d'export CSV en développement")
    
    def save_chart(self):
        """Sauvegarde le graphique"""
        messagebox.showinfo("Sauvegarde", "Fonctionnalité de sauvegarde en développement")
    
    def toggle_fullscreen(self):
        """Bascule en plein écran"""
        self.window.attributes('-fullscreen', not self.window.attributes('-fullscreen'))
    
    def center_window(self):
        """Centre la fenêtre"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")

def show_results_viewer(parent, results_file=None):
    """Affiche le visualiseur de résultats"""
    return ResultsViewer(parent, results_file)
