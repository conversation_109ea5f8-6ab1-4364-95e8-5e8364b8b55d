@echo off
title Système de Correction Automatique
color 0A

echo.
echo ========================================
echo   SYSTEME DE CORRECTION AUTOMATIQUE
echo ========================================
echo.
echo Lancement de l'interface graphique...
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installé ou pas dans le PATH
    echo Veuillez installer Python 3.8+ depuis https://python.org
    pause
    exit /b 1
)

REM Lancer l'interface
python launch_gui.py

if errorlevel 1 (
    echo.
    echo ERREUR lors du lancement de l'interface
    echo Consultez les messages d'erreur ci-dessus
    pause
)

echo.
echo Interface fermée.
pause
