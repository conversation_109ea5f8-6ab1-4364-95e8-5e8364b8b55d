# 🖥️ Guide d'Utilisation de l'Interface Graphique

## 🚀 Démarrage Rapide

### 1. Lancement de l'Interface

#### Sur Windows :
```bash
# Double-cliquez sur le fichier
lancer_interface.bat
```

#### Sur tous les systèmes :
```bash
python launch_gui.py
```

### 2. Écran d'Accueil

L'écran d'accueil vous propose trois options :

- **🚀 Lancer l'Interface Principale** : Interface complète de correction
- **⚙️ Configuration** : Paramètres avancés (disponible dans l'interface principale)
- **🧪 Tests du Système** : Vérification des composants

## 📋 Interface Principale

### Section Fichiers 📁

1. **Prototype PDF** : Sélectionnez votre corrigé type
   - Cliquez sur "Parcourir" pour choisir le fichier
   - Le fichier doit contenir les questions et réponses attendues

2. **Fichier élèves** : Liste des étudiants à corriger
   - Format supporté : fichier texte (.txt)
   - Formats de ligne acceptés :
     ```
     Nom Prénom
     ID:123 Nom Prénom - Classe
     Nom, Prénom
     ```

3. **Examen PDF** : Fichier contenant les questions de l'examen
   - Doit être au format PDF
   - Contient les questions auxquelles les élèves ont répondu

4. **Résultats** : Fichier de sortie pour les résultats
   - Format Excel (.xlsx) recommandé
   - Sera créé automatiquement

### Section Configuration ⚙️

- **Modèle IA** : Affiche le modèle Llama 3.3 utilisé
- **Note max** : Note maximale (par défaut 20)
- **Connexion IA** : Statut de la connexion
  - Cliquez sur "Tester" pour vérifier

### Section Contrôles 🎮

1. **🧪 Tester le système** : Lance les tests de vérification
   - Vérifie tous les composants
   - Affiche les résultats dans les logs

2. **🚀 Démarrer la correction** : Lance le processus complet
   - Vérifie les fichiers requis
   - Démarre la correction automatique
   - Affiche la progression

3. **⏹️ Arrêter** : Interrompt la correction en cours

4. **📊 Voir résultats** : Ouvre le visualiseur de résultats
   - Disponible après une correction réussie
   - Interface interactive avec graphiques

5. **⚙️ Configuration** : Ouvre les paramètres avancés

### Section Logs 📋

- Affiche en temps réel le déroulement des opérations
- Messages horodatés avec codes couleur
- Bouton "🗑️ Effacer logs" pour nettoyer l'affichage

## ⚙️ Configuration Avancée

Accessible via le bouton "⚙️ Configuration" dans l'interface principale.

### Onglet Intelligence Artificielle 🤖

- **Clé API** : Votre clé OpenRouter (masquée par défaut)
- **Modèle** : Sélection du modèle IA
- **Température** : Contrôle la créativité (0-1)
- **Tokens max** : Limite de tokens par réponse
- **Test de connexion** : Vérification en temps réel

### Onglet Fichiers 📁

- **Chemins par défaut** : Définit les emplacements par défaut
- **Options** : Sauvegarde automatique, sauvegardes
- Configuration persistante entre les sessions

### Onglet Interface 🎨

- **Thème** : Apparence de l'interface
- **Options d'affichage** : Logs détaillés, etc.
- **Prévisualisation** : Aperçu des changements

### Onglet Avancé ⚙️

- **Paramètres de correction** : Note maximale, critères
- **Prompt système** : Personnalisation des instructions IA
- **Sauvegarde/Restauration** : Gestion des configurations

## 📊 Visualiseur de Résultats

Accessible après une correction réussie via "📊 Voir résultats".

### Onglet Résumé 📋

- **Statistiques générales** : Nombre d'étudiants, moyennes, etc.
- **Répartition des mentions** : Distribution des notes
- **Performances** : Meilleurs et moins bons résultats

### Onglet Détails 📝

- **Tableau interactif** : Tous les résultats en détail
- **Recherche** : Filtrage par nom, classe, etc.
- **Tri** : Classement par colonne
- **Double-clic** : Détails complets d'un étudiant

### Onglet Graphiques 📈

- **Types de graphiques** :
  - Histogramme : Distribution des notes
  - Camembert : Répartition des mentions
  - Barres : Moyennes par classe
  - Boîte à moustaches : Analyse statistique

- **Contrôles** : Actualisation, sauvegarde
- **Interactivité** : Zoom, navigation

### Onglet Statistiques 📊

- **Statistiques descriptives** : Moyenne, médiane, écart-type
- **Quartiles** : Q1, Q2, Q3
- **Comparaisons** : Entre classes, groupes
- **Export** : Données statistiques

## 🔧 Dépannage Interface

### Problèmes Courants

1. **Interface ne se lance pas**
   ```bash
   # Vérifiez les dépendances
   pip install -r requirements.txt
   
   # Testez Python
   python --version
   ```

2. **Erreur "Module non trouvé"**
   ```bash
   # Installez tkinter (si nécessaire)
   sudo apt-get install python3-tk  # Linux
   ```

3. **Graphiques ne s'affichent pas**
   ```bash
   # Installez matplotlib
   pip install matplotlib
   ```

4. **Fichiers PDF non reconnus**
   - Vérifiez que le PDF n'est pas protégé
   - Essayez avec un autre fichier PDF
   - Consultez les logs pour plus de détails

### Messages d'Erreur Fréquents

- **"Fichiers manquants"** : Vérifiez les chemins des fichiers
- **"Connexion IA échouée"** : Vérifiez votre connexion internet et la clé API
- **"Erreur d'extraction PDF"** : Le PDF peut être corrompu ou protégé

## 💡 Conseils d'Utilisation

### Préparation des Fichiers

1. **Prototype PDF** :
   - Structurez clairement questions et réponses
   - Utilisez des titres explicites
   - Évitez les images complexes

2. **Fichier élèves** :
   - Une ligne par élève
   - Format cohérent
   - Évitez les caractères spéciaux

3. **Examen PDF** :
   - Questions numérotées
   - Texte lisible
   - Structure similaire au prototype

### Optimisation des Performances

- **Testez d'abord** avec un petit groupe d'élèves
- **Vérifiez la connexion IA** avant de lancer une correction complète
- **Sauvegardez** vos configurations personnalisées
- **Consultez les logs** en cas de problème

### Workflow Recommandé

1. 🧪 **Tests** : Lancez les tests système
2. ⚙️ **Configuration** : Vérifiez les paramètres
3. 📁 **Fichiers** : Sélectionnez tous les fichiers requis
4. 🔗 **Connexion** : Testez la connexion IA
5. 🚀 **Correction** : Lancez le processus
6. 📊 **Résultats** : Analysez les résultats
7. 💾 **Sauvegarde** : Exportez si nécessaire

## 🆘 Support

En cas de problème :

1. **Consultez les logs** dans l'interface
2. **Vérifiez le fichier** `correction_system.log`
3. **Testez les composants** individuellement
4. **Vérifiez la configuration** dans `config.py`

## 🔄 Mises à Jour

L'interface se met à jour automatiquement avec les nouveaux fichiers. Pour forcer une actualisation :

1. Fermez l'interface
2. Relancez `launch_gui.py`
3. Les nouvelles fonctionnalités seront disponibles

---

**Note** : Cette interface est conçue pour être intuitive. N'hésitez pas à explorer les différentes fonctionnalités et à consulter les tooltips pour plus d'informations.
