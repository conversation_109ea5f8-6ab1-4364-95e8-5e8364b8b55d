"""
Module pour ajouter le support du glisser-déposer dans l'interface
"""

import tkinter as tk
from tkinter import ttk
import os

class DragDropMixin:
    """Mixin pour ajouter le support du glisser-déposer"""
    
    def __init__(self):
        self.drag_drop_enabled = True
    
    def enable_drag_drop(self, widget, callback, file_types=None):
        """
        Active le glisser-déposer sur un widget
        
        Args:
            widget: Widget tkinter
            callback: Fonction appelée avec le chemin du fichier
            file_types: Liste des extensions acceptées (ex: ['.pdf', '.txt'])
        """
        if not self.drag_drop_enabled:
            return
        
        # Stocker les informations
        widget._drag_drop_callback = callback
        widget._drag_drop_file_types = file_types or []
        
        # Bind les événements
        widget.bind('<Button-1>', self._on_drag_start)
        widget.bind('<B1-Motion>', self._on_drag_motion)
        widget.bind('<ButtonRelease-1>', self._on_drag_end)
        
        # Événements pour le drop
        widget.bind('<Enter>', self._on_drag_enter)
        widget.bind('<Leave>', self._on_drag_leave)
        
        # Changer l'apparence pour indiquer le support du drag&drop
        self._setup_drag_drop_appearance(widget)
    
    def _setup_drag_drop_appearance(self, widget):
        """Configure l'apparence pour indiquer le support du drag&drop"""
        try:
            # Ajouter un tooltip
            self._create_tooltip(widget, "💡 Glissez-déposez un fichier ici ou cliquez pour parcourir")
            
            # Changer le curseur
            widget.configure(cursor="hand2")
            
        except Exception:
            pass  # Ignore les erreurs de configuration
    
    def _create_tooltip(self, widget, text):
        """Crée un tooltip pour le widget"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            
            label = tk.Label(tooltip, text=text, background="lightyellow", 
                           relief="solid", borderwidth=1, font=("Arial", 8))
            label.pack()
            
            widget._tooltip = tooltip
        
        def on_leave(event):
            if hasattr(widget, '_tooltip'):
                widget._tooltip.destroy()
                del widget._tooltip
        
        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)
    
    def _on_drag_start(self, event):
        """Début du glisser"""
        widget = event.widget
        widget._drag_start_x = event.x
        widget._drag_start_y = event.y
    
    def _on_drag_motion(self, event):
        """Mouvement pendant le glisser"""
        pass  # Peut être étendu pour des effets visuels
    
    def _on_drag_end(self, event):
        """Fin du glisser"""
        pass  # Peut être étendu pour des actions spécifiques
    
    def _on_drag_enter(self, event):
        """Entrée dans la zone de drop"""
        widget = event.widget
        try:
            # Changer l'apparence pour indiquer qu'on peut dropper
            original_bg = widget.cget('background')
            widget._original_bg = original_bg
            widget.configure(background='lightblue')
        except Exception:
            pass
    
    def _on_drag_leave(self, event):
        """Sortie de la zone de drop"""
        widget = event.widget
        try:
            # Restaurer l'apparence originale
            if hasattr(widget, '_original_bg'):
                widget.configure(background=widget._original_bg)
        except Exception:
            pass
    
    def simulate_file_drop(self, widget, file_path):
        """
        Simule un drop de fichier (pour les tests)
        
        Args:
            widget: Widget cible
            file_path: Chemin du fichier
        """
        if not os.path.exists(file_path):
            return False
        
        # Vérifier le type de fichier
        if hasattr(widget, '_drag_drop_file_types'):
            file_types = widget._drag_drop_file_types
            if file_types:
                file_ext = os.path.splitext(file_path)[1].lower()
                if file_ext not in file_types:
                    return False
        
        # Appeler le callback
        if hasattr(widget, '_drag_drop_callback'):
            try:
                widget._drag_drop_callback(file_path)
                return True
            except Exception as e:
                print(f"Erreur dans le callback drag&drop: {e}")
                return False
        
        return False

class EnhancedEntry(tk.Entry, DragDropMixin):
    """Entry amélioré avec support du glisser-déposer"""
    
    def __init__(self, parent, textvariable=None, file_types=None, **kwargs):
        tk.Entry.__init__(self, parent, textvariable=textvariable, **kwargs)
        DragDropMixin.__init__(self)
        
        self.textvariable = textvariable
        
        # Activer le drag&drop
        self.enable_drag_drop(self, self._on_file_dropped, file_types)
        
        # Bind double-clic pour ouvrir le dialogue
        self.bind('<Double-Button-1>', self._on_double_click)
    
    def _on_file_dropped(self, file_path):
        """Appelé quand un fichier est déposé"""
        if self.textvariable:
            self.textvariable.set(file_path)
        else:
            self.delete(0, tk.END)
            self.insert(0, file_path)
        
        # Déclencher un événement personnalisé
        self.event_generate('<<FileDropped>>')
    
    def _on_double_click(self, event):
        """Appelé lors d'un double-clic"""
        # Déclencher un événement pour ouvrir le dialogue
        self.event_generate('<<BrowseFile>>')

class FileSelector(ttk.Frame, DragDropMixin):
    """Widget de sélection de fichier avec drag&drop"""
    
    def __init__(self, parent, label_text="Fichier:", file_types=None, **kwargs):
        ttk.Frame.__init__(self, parent, **kwargs)
        DragDropMixin.__init__(self)
        
        self.file_path = tk.StringVar()
        self.file_types = file_types or []
        self.callback = None
        
        self.setup_ui(label_text)
    
    def setup_ui(self, label_text):
        """Configure l'interface du sélecteur"""
        # Label
        self.label = ttk.Label(self, text=label_text)
        self.label.grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        # Entry avec drag&drop
        self.entry = EnhancedEntry(self, textvariable=self.file_path, 
                                  file_types=self.file_types, width=50)
        self.entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # Bouton parcourir
        self.browse_button = ttk.Button(self, text="📁 Parcourir", 
                                       command=self.browse_file)
        self.browse_button.grid(row=0, column=2)
        
        # Bouton effacer
        self.clear_button = ttk.Button(self, text="🗑️", command=self.clear_file)
        self.clear_button.grid(row=0, column=3, padx=(5, 0))
        
        # Configuration du grid
        self.columnconfigure(1, weight=1)
        
        # Bind les événements
        self.entry.bind('<<FileDropped>>', self._on_file_changed)
        self.entry.bind('<<BrowseFile>>', lambda e: self.browse_file())
    
    def browse_file(self):
        """Ouvre le dialogue de sélection de fichier"""
        from tkinter import filedialog
        
        # Déterminer les types de fichiers
        if self.file_types:
            if '.pdf' in self.file_types:
                filetypes = [("Fichiers PDF", "*.pdf"), ("Tous les fichiers", "*.*")]
            elif '.txt' in self.file_types:
                filetypes = [("Fichiers texte", "*.txt"), ("Tous les fichiers", "*.*")]
            elif '.xlsx' in self.file_types:
                filetypes = [("Fichiers Excel", "*.xlsx"), ("Tous les fichiers", "*.*")]
            else:
                filetypes = [("Tous les fichiers", "*.*")]
        else:
            filetypes = [("Tous les fichiers", "*.*")]
        
        filename = filedialog.askopenfilename(
            title=f"Sélectionner {self.label.cget('text')}",
            filetypes=filetypes
        )
        
        if filename:
            self.file_path.set(filename)
            self._on_file_changed()
    
    def clear_file(self):
        """Efface le fichier sélectionné"""
        self.file_path.set("")
        self._on_file_changed()
    
    def _on_file_changed(self, event=None):
        """Appelé quand le fichier change"""
        if self.callback:
            self.callback(self.file_path.get())
        
        # Changer la couleur selon l'état
        file_path = self.file_path.get()
        if file_path and os.path.exists(file_path):
            self.entry.configure(foreground='green')
        elif file_path:
            self.entry.configure(foreground='red')
        else:
            self.entry.configure(foreground='black')
    
    def set_callback(self, callback):
        """Définit le callback appelé lors du changement de fichier"""
        self.callback = callback
    
    def get_file_path(self):
        """Retourne le chemin du fichier sélectionné"""
        return self.file_path.get()
    
    def set_file_path(self, path):
        """Définit le chemin du fichier"""
        self.file_path.set(path)
        self._on_file_changed()

class ProgressDialog:
    """Dialogue de progression avec annulation"""
    
    def __init__(self, parent, title="Progression", message="Traitement en cours..."):
        self.parent = parent
        self.cancelled = False
        
        # Créer la fenêtre
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry("400x150")
        self.window.transient(parent)
        self.window.grab_set()
        
        # Centrer la fenêtre
        self.center_window()
        
        # Interface
        self.setup_ui(message)
        
        # Empêcher la fermeture
        self.window.protocol("WM_DELETE_WINDOW", self.cancel)
    
    def setup_ui(self, message):
        """Configure l'interface du dialogue"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Message
        self.message_label = ttk.Label(main_frame, text=message, font=('Arial', 10))
        self.message_label.pack(pady=(0, 10))
        
        # Barre de progression
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(0, 10))
        self.progress.start()
        
        # Détails
        self.detail_label = ttk.Label(main_frame, text="", font=('Arial', 8))
        self.detail_label.pack(pady=(0, 10))
        
        # Bouton annuler
        self.cancel_button = ttk.Button(main_frame, text="Annuler", command=self.cancel)
        self.cancel_button.pack()
    
    def update_message(self, message):
        """Met à jour le message principal"""
        self.message_label.config(text=message)
        self.window.update()
    
    def update_detail(self, detail):
        """Met à jour les détails"""
        self.detail_label.config(text=detail)
        self.window.update()
    
    def cancel(self):
        """Annule l'opération"""
        self.cancelled = True
        self.close()
    
    def close(self):
        """Ferme le dialogue"""
        self.progress.stop()
        self.window.destroy()
    
    def center_window(self):
        """Centre la fenêtre"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")

def create_enhanced_file_section(parent, start_row):
    """
    Crée une section de fichiers améliorée avec drag&drop
    
    Args:
        parent: Widget parent
        start_row: Ligne de départ
        
    Returns:
        Dictionnaire des sélecteurs de fichiers
    """
    # Titre section
    files_label = ttk.Label(parent, text="📁 Fichiers (Glissez-déposez ou cliquez)", 
                           font=('Arial', 12, 'bold'))
    files_label.grid(row=start_row, column=0, columnspan=3, sticky=tk.W, pady=(10, 5))
    
    # Sélecteurs de fichiers
    selectors = {}
    
    # Prototype PDF
    prototype_selector = FileSelector(parent, "Prototype PDF:", ['.pdf'])
    prototype_selector.grid(row=start_row+1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=2)
    selectors['prototype'] = prototype_selector
    
    # Fichier élèves
    students_selector = FileSelector(parent, "Fichier élèves:", ['.txt'])
    students_selector.grid(row=start_row+2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=2)
    selectors['students'] = students_selector
    
    # Examen PDF
    exam_selector = FileSelector(parent, "Examen PDF:", ['.pdf'])
    exam_selector.grid(row=start_row+3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=2)
    selectors['exam'] = exam_selector
    
    # Fichier résultats
    results_selector = FileSelector(parent, "Résultats:", ['.xlsx'])
    results_selector.grid(row=start_row+4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=2)
    selectors['results'] = results_selector
    
    # Configuration du grid
    parent.columnconfigure(0, weight=1)
    
    return selectors
