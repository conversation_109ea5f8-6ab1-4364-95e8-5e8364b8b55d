"""
Module d'analyse des fichiers PDF
"""

import PyPDF2
import pdfplumber
from typing import Dict, List, Optional
import logging

class PDFAnalyzer:
    """Classe pour analyser les fichiers PDF"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_text_pypdf2(self, pdf_path: str) -> str:
        """
        Extrait le texte d'un PDF avec PyPDF2
        
        Args:
            pdf_path: Chemin vers le fichier PDF
            
        Returns:
            Texte extrait du PDF
        """
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text() + "\n"
                
                return text.strip()
        except Exception as e:
            self.logger.error(f"Erreur lors de l'extraction avec PyPDF2: {e}")
            return ""
    
    def extract_text_pdfplumber(self, pdf_path: str) -> str:
        """
        Extrait le texte d'un PDF avec pdfplumber (meilleure qualité)
        
        Args:
            pdf_path: Chemin vers le fichier PDF
            
        Returns:
            Texte extrait du PDF
        """
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            
            return text.strip()
        except Exception as e:
            self.logger.error(f"Erreur lors de l'extraction avec pdfplumber: {e}")
            return ""
    
    def extract_text(self, pdf_path: str, method: str = "pdfplumber") -> str:
        """
        Extrait le texte d'un PDF avec la méthode spécifiée
        
        Args:
            pdf_path: Chemin vers le fichier PDF
            method: Méthode d'extraction ("pdfplumber" ou "pypdf2")
            
        Returns:
            Texte extrait du PDF
        """
        if method == "pdfplumber":
            text = self.extract_text_pdfplumber(pdf_path)
            if not text:  # Fallback vers PyPDF2 si pdfplumber échoue
                text = self.extract_text_pypdf2(pdf_path)
        else:
            text = self.extract_text_pypdf2(pdf_path)
        
        return text
    
    def analyze_prototype(self, pdf_path: str) -> Dict[str, str]:
        """
        Analyse le prototype de correction
        
        Args:
            pdf_path: Chemin vers le fichier prototype PDF
            
        Returns:
            Dictionnaire contenant les informations du prototype
        """
        text = self.extract_text(pdf_path)
        
        return {
            "content": text,
            "questions": self._extract_questions(text),
            "answers": self._extract_answers(text)
        }
    
    def analyze_exam(self, pdf_path: str) -> Dict[str, str]:
        """
        Analyse le fichier d'examen
        
        Args:
            pdf_path: Chemin vers le fichier examen PDF
            
        Returns:
            Dictionnaire contenant les informations de l'examen
        """
        text = self.extract_text(pdf_path)
        
        return {
            "content": text,
            "questions": self._extract_questions(text),
            "structure": self._analyze_exam_structure(text)
        }
    
    def _extract_questions(self, text: str) -> List[str]:
        """
        Extrait les questions du texte
        
        Args:
            text: Texte à analyser
            
        Returns:
            Liste des questions trouvées
        """
        questions = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            # Recherche de patterns de questions
            if any(pattern in line.lower() for pattern in ['question', 'exercice', 'problème', '?']):
                if len(line) > 10:  # Éviter les lignes trop courtes
                    questions.append(line)
        
        return questions
    
    def _extract_answers(self, text: str) -> List[str]:
        """
        Extrait les réponses du prototype
        
        Args:
            text: Texte à analyser
            
        Returns:
            Liste des réponses trouvées
        """
        answers = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            # Recherche de patterns de réponses
            if any(pattern in line.lower() for pattern in ['réponse', 'solution', 'correction']):
                if len(line) > 10:
                    answers.append(line)
        
        return answers
    
    def _analyze_exam_structure(self, text: str) -> Dict[str, any]:
        """
        Analyse la structure de l'examen
        
        Args:
            text: Texte de l'examen
            
        Returns:
            Dictionnaire avec la structure de l'examen
        """
        return {
            "total_lines": len(text.split('\n')),
            "total_chars": len(text),
            "estimated_questions": len(self._extract_questions(text))
        }
