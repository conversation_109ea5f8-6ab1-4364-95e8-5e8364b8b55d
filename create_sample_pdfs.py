"""
Générateur de fichiers PDF d'exemple pour tester le système
"""

from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
import os

def create_prototype_pdf():
    """Crée un fichier prototype.pdf d'exemple"""
    
    filename = "prototype.pdf"
    doc = SimpleDocTemplate(filename, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # Titre
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center
    )
    
    story.append(Paragraph("PROTOTYPE DE CORRECTION", title_style))
    story.append(Paragraph("Examen de Mathématiques - Niveau Terminale", styles['Heading2']))
    story.append(Spacer(1, 20))
    
    # Instructions
    story.append(Paragraph("Instructions de correction :", styles['Heading3']))
    instructions = """
    Ce document contient les réponses attendues et les critères de notation pour l'examen de mathématiques.
    Chaque question est notée sur le nombre de points indiqué.
    Accordez des points partiels selon la qualité du raisonnement.
    """
    story.append(Paragraph(instructions, styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Question 1
    story.append(Paragraph("Question 1 (5 points) : Résolution d'équation", styles['Heading3']))
    story.append(Paragraph("Énoncé : Résolvez l'équation 2x + 3 = 11", styles['Normal']))
    story.append(Paragraph("Réponse attendue :", styles['Heading4']))
    reponse1 = """
    2x + 3 = 11
    2x = 11 - 3
    2x = 8
    x = 4
    
    Critères de notation :
    - Isolation correcte du terme 2x : 2 points
    - Calcul correct de 11-3 : 1 point  
    - Division par 2 correcte : 1 point
    - Réponse finale x=4 : 1 point
    """
    story.append(Paragraph(reponse1, styles['Normal']))
    story.append(Spacer(1, 15))
    
    # Question 2
    story.append(Paragraph("Question 2 (7 points) : Fonction dérivée", styles['Heading3']))
    story.append(Paragraph("Énoncé : Calculez la dérivée de f(x) = x³ + 2x² - 5x + 1", styles['Normal']))
    story.append(Paragraph("Réponse attendue :", styles['Heading4']))
    reponse2 = """
    f(x) = x³ + 2x² - 5x + 1
    f'(x) = 3x² + 4x - 5
    
    Critères de notation :
    - Dérivée de x³ = 3x² : 2 points
    - Dérivée de 2x² = 4x : 2 points
    - Dérivée de -5x = -5 : 2 points
    - Dérivée de la constante = 0 : 1 point
    """
    story.append(Paragraph(reponse2, styles['Normal']))
    story.append(Spacer(1, 15))
    
    # Question 3
    story.append(Paragraph("Question 3 (8 points) : Géométrie", styles['Heading3']))
    story.append(Paragraph("Énoncé : Dans un triangle rectangle ABC, AB = 3 cm et BC = 4 cm. Calculez AC.", styles['Normal']))
    story.append(Paragraph("Réponse attendue :", styles['Heading4']))
    reponse3 = """
    Triangle rectangle en B
    D'après le théorème de Pythagore : AC² = AB² + BC²
    AC² = 3² + 4²
    AC² = 9 + 16
    AC² = 25
    AC = 5 cm
    
    Critères de notation :
    - Identification du triangle rectangle : 1 point
    - Application du théorème de Pythagore : 2 points
    - Calculs corrects (9 + 16 = 25) : 3 points
    - Racine carrée correcte : 1 point
    - Unité et réponse finale : 1 point
    """
    story.append(Paragraph(reponse3, styles['Normal']))
    
    # Barème total
    story.append(Spacer(1, 20))
    story.append(Paragraph("BARÈME TOTAL : 20 points", styles['Heading3']))
    
    # Tableau de notation
    data = [
        ['Question', 'Points', 'Compétences évaluées'],
        ['Question 1', '5 pts', 'Résolution d\'équations'],
        ['Question 2', '7 pts', 'Calcul de dérivées'],
        ['Question 3', '8 pts', 'Géométrie - Pythagore'],
        ['TOTAL', '20 pts', '']
    ]
    
    table = Table(data, colWidths=[2*inch, 1*inch, 3*inch])
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    story.append(table)
    
    # Construire le PDF
    doc.build(story)
    print(f"✅ Fichier {filename} créé avec succès")

def create_exam_pdf():
    """Crée un fichier examen.pdf d'exemple"""
    
    filename = "examen.pdf"
    doc = SimpleDocTemplate(filename, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []
    
    # Titre
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center
    )
    
    story.append(Paragraph("EXAMEN DE MATHÉMATIQUES", title_style))
    story.append(Paragraph("Terminale - Durée : 2 heures", styles['Heading2']))
    story.append(Spacer(1, 20))
    
    # Instructions
    story.append(Paragraph("Instructions :", styles['Heading3']))
    instructions = """
    • Répondez à toutes les questions sur votre copie
    • Justifiez vos réponses et détaillez vos calculs
    • La qualité de la rédaction sera prise en compte
    • Calculatrice autorisée
    """
    story.append(Paragraph(instructions, styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Question 1
    story.append(Paragraph("Question 1 (5 points)", styles['Heading3']))
    story.append(Paragraph("Résolvez l'équation suivante :", styles['Normal']))
    story.append(Paragraph("2x + 3 = 11", styles['Heading4']))
    story.append(Spacer(1, 30))
    
    # Espace pour la réponse
    story.append(Paragraph("Réponse :", styles['Normal']))
    story.append(Spacer(1, 60))
    
    # Question 2
    story.append(Paragraph("Question 2 (7 points)", styles['Heading3']))
    story.append(Paragraph("Soit la fonction f définie par :", styles['Normal']))
    story.append(Paragraph("f(x) = x³ + 2x² - 5x + 1", styles['Heading4']))
    story.append(Paragraph("Calculez la dérivée f'(x) de cette fonction.", styles['Normal']))
    story.append(Spacer(1, 30))
    
    # Espace pour la réponse
    story.append(Paragraph("Réponse :", styles['Normal']))
    story.append(Spacer(1, 60))
    
    # Question 3
    story.append(Paragraph("Question 3 (8 points)", styles['Heading3']))
    story.append(Paragraph("Problème de géométrie :", styles['Normal']))
    story.append(Paragraph("Dans un triangle rectangle ABC rectangle en B, on donne :", styles['Normal']))
    story.append(Paragraph("• AB = 3 cm", styles['Normal']))
    story.append(Paragraph("• BC = 4 cm", styles['Normal']))
    story.append(Paragraph("Calculez la longueur AC.", styles['Normal']))
    story.append(Spacer(1, 30))
    
    # Espace pour la réponse
    story.append(Paragraph("Réponse :", styles['Normal']))
    story.append(Spacer(1, 60))
    
    # Fin
    story.append(Spacer(1, 40))
    story.append(Paragraph("Fin de l'examen", styles['Heading3']))
    story.append(Paragraph("Vérifiez vos réponses avant de rendre votre copie.", styles['Normal']))
    
    # Construire le PDF
    doc.build(story)
    print(f"✅ Fichier {filename} créé avec succès")

def main():
    """Fonction principale"""
    print("📄 Création des fichiers PDF d'exemple...")
    print()
    
    try:
        # Vérifier si reportlab est installé
        import reportlab
        print("✅ Module reportlab détecté")
    except ImportError:
        print("❌ Module reportlab non trouvé")
        print("💡 Installation en cours...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])
        print("✅ Module reportlab installé")
    
    try:
        create_prototype_pdf()
        create_exam_pdf()
        
        print()
        print("🎉 Fichiers PDF créés avec succès !")
        print("📁 Fichiers disponibles :")
        print("   • prototype.pdf - Corrigé type")
        print("   • examen.pdf - Questions d'examen")
        print("   • eleves.txt - Liste des élèves")
        print()
        print("🚀 Vous pouvez maintenant tester le système complet !")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des PDF : {e}")
        print("💡 Vérifiez que le module reportlab est installé")

if __name__ == "__main__":
    main()
