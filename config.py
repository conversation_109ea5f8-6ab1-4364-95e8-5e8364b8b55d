"""
Configuration du système de correction automatique
"""

# Configuration de l'API Llama 3.3
OPENROUTER_API_KEY = "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"
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
MODEL_NAME = "meta-llama/llama-3.3-70b-instruct:free"

# Configuration des fichiers
PROTOTYPE_PDF_PATH = "prototype.pdf"
STUDENTS_FILE_PATH = "eleves.txt"
EXAM_PDF_PATH = "examen.pdf"
RESULTS_FILE_PATH = "resultats.xlsx"

# Configuration de la correction
MAX_SCORE = 20  # Note maximale
TEMPERATURE = 0.3  # Température pour l'IA (plus bas = plus déterministe)
MAX_TOKENS = 2000  # Nombre maximum de tokens pour la réponse

# Messages système pour l'IA
SYSTEM_PROMPT = """
Vous êtes un correcteur automatique de tests. Votre rôle est d'analyser les réponses des étudiants 
et de les comparer au prototype de correction pour attribuer une note juste et objective.

Instructions de correction :
1. Analysez chaque réponse en détail
2. Comparez avec le prototype de correction
3. Attribuez une note sur 20 points
4. Justifiez brièvement votre notation
5. Soyez équitable et cohérent dans vos évaluations
"""
