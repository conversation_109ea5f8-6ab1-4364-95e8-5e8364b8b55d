"""
Script de lancement de l'interface graphique du système de correction automatique
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Vérifie les dépendances nécessaires"""
    missing_modules = []
    
    # Modules requis
    required_modules = [
        'tkinter',
        'pandas',
        'requests',
        'PyPDF2',
        'pdfplumber',
        'openpyxl'
    ]
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    return missing_modules

def show_welcome_screen():
    """Affiche l'écran d'accueil"""
    welcome = tk.Tk()
    welcome.title("Système de Correction Automatique")
    welcome.geometry("500x400")
    welcome.resizable(False, False)
    
    # Centrer la fenêtre
    welcome.update_idletasks()
    x = (welcome.winfo_screenwidth() // 2) - (welcome.winfo_width() // 2)
    y = (welcome.winfo_screenheight() // 2) - (welcome.winfo_height() // 2)
    welcome.geometry(f"+{x}+{y}")
    
    # Contenu
    main_frame = tk.Frame(welcome, bg='white', padx=20, pady=20)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # Titre
    title_label = tk.Label(main_frame, text="🤖 Système de Correction Automatique", 
                          font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
    title_label.pack(pady=(0, 10))
    
    # Sous-titre
    subtitle_label = tk.Label(main_frame, text="Powered by Llama 3.3 8B Instruct", 
                             font=('Arial', 12), bg='white', fg='#7f8c8d')
    subtitle_label.pack(pady=(0, 20))
    
    # Description
    description = """
Ce système utilise l'intelligence artificielle pour corriger automatiquement 
des tests en analysant un prototype de correction et les copies d'élèves.

Fonctionnalités principales :
• 📄 Analyse de fichiers PDF (prototype et examen)
• 👥 Gestion des listes d'élèves
• 🤖 Correction automatique par IA
• 📊 Génération de rapports détaillés
• 📈 Visualisation des résultats
"""
    
    desc_label = tk.Label(main_frame, text=description, font=('Arial', 10), 
                         bg='white', fg='#34495e', justify=tk.LEFT)
    desc_label.pack(pady=(0, 20))
    
    # Boutons
    button_frame = tk.Frame(main_frame, bg='white')
    button_frame.pack(pady=20)
    
    def launch_main():
        welcome.destroy()
        launch_main_interface()
    
    def launch_config():
        welcome.destroy()
        launch_configuration()
    
    def launch_tests():
        welcome.destroy()
        launch_test_interface()
    
    # Bouton principal
    main_button = tk.Button(button_frame, text="🚀 Lancer l'Interface Principale", 
                           command=launch_main, font=('Arial', 12, 'bold'),
                           bg='#3498db', fg='white', padx=20, pady=10,
                           relief=tk.FLAT, cursor='hand2')
    main_button.pack(pady=5)
    
    # Bouton configuration
    config_button = tk.Button(button_frame, text="⚙️ Configuration", 
                             command=launch_config, font=('Arial', 10),
                             bg='#95a5a6', fg='white', padx=20, pady=8,
                             relief=tk.FLAT, cursor='hand2')
    config_button.pack(pady=5)
    
    # Bouton tests
    test_button = tk.Button(button_frame, text="🧪 Tests du Système", 
                           command=launch_tests, font=('Arial', 10),
                           bg='#e67e22', fg='white', padx=20, pady=8,
                           relief=tk.FLAT, cursor='hand2')
    test_button.pack(pady=5)
    
    # Informations système
    info_frame = tk.Frame(main_frame, bg='#ecf0f1', relief=tk.SUNKEN, bd=1)
    info_frame.pack(fill=tk.X, pady=(20, 0))
    
    info_text = f"Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} | " \
                f"Répertoire: {os.getcwd()}"
    info_label = tk.Label(info_frame, text=info_text, font=('Arial', 8), 
                         bg='#ecf0f1', fg='#7f8c8d', pady=5)
    info_label.pack()
    
    welcome.mainloop()

def launch_main_interface():
    """Lance l'interface principale"""
    try:
        from gui_main import main
        main()
    except ImportError as e:
        messagebox.showerror("Erreur", f"Impossible de charger l'interface principale:\n{e}")
    except Exception as e:
        messagebox.showerror("Erreur", f"Erreur lors du lancement:\n{e}")

def launch_configuration():
    """Lance l'interface de configuration"""
    try:
        # Interface de configuration simple
        config_window = tk.Tk()
        config_window.title("Configuration Rapide")
        config_window.geometry("400x300")
        
        tk.Label(config_window, text="Configuration Rapide", 
                font=('Arial', 14, 'bold')).pack(pady=20)
        
        tk.Label(config_window, text="Cette fonctionnalité sera disponible\ndans l'interface principale.",
                font=('Arial', 10)).pack(pady=20)
        
        tk.Button(config_window, text="Ouvrir Interface Principale", 
                 command=lambda: [config_window.destroy(), launch_main_interface()],
                 bg='#3498db', fg='white', padx=20, pady=10).pack(pady=20)
        
        config_window.mainloop()
        
    except Exception as e:
        messagebox.showerror("Erreur", f"Erreur lors du lancement de la configuration:\n{e}")

def launch_test_interface():
    """Lance l'interface de test"""
    try:
        # Interface de test simple
        test_window = tk.Tk()
        test_window.title("Tests du Système")
        test_window.geometry("600x400")
        
        tk.Label(test_window, text="🧪 Tests du Système", 
                font=('Arial', 14, 'bold')).pack(pady=20)
        
        # Zone de texte pour les résultats
        import tkinter.scrolledtext as scrolledtext
        result_text = scrolledtext.ScrolledText(test_window, height=15, width=70)
        result_text.pack(padx=20, pady=10, fill=tk.BOTH, expand=True)
        
        def run_tests():
            result_text.delete(1.0, tk.END)
            result_text.insert(tk.END, "🔄 Lancement des tests...\n\n")
            result_text.update()
            
            try:
                from test_system import main as run_test_main
                import io
                import contextlib
                
                # Capturer la sortie
                f = io.StringIO()
                with contextlib.redirect_stdout(f):
                    run_test_main()
                
                output = f.getvalue()
                result_text.insert(tk.END, output)
                
            except Exception as e:
                result_text.insert(tk.END, f"❌ Erreur lors des tests: {e}\n")
            
            result_text.see(tk.END)
        
        button_frame = tk.Frame(test_window)
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="▶️ Lancer les Tests", command=run_tests,
                 bg='#e67e22', fg='white', padx=20, pady=8).pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="🏠 Retour Accueil", 
                 command=lambda: [test_window.destroy(), show_welcome_screen()],
                 bg='#95a5a6', fg='white', padx=20, pady=8).pack(side=tk.LEFT, padx=5)
        
        test_window.mainloop()
        
    except Exception as e:
        messagebox.showerror("Erreur", f"Erreur lors du lancement des tests:\n{e}")

def main():
    """Fonction principale"""
    print("🚀 Lancement du Système de Correction Automatique...")
    
    # Vérification des dépendances
    missing = check_dependencies()
    
    if missing:
        # Interface d'erreur simple
        error_window = tk.Tk()
        error_window.title("Dépendances Manquantes")
        error_window.geometry("500x300")
        
        tk.Label(error_window, text="❌ Dépendances Manquantes", 
                font=('Arial', 14, 'bold'), fg='red').pack(pady=20)
        
        tk.Label(error_window, text="Les modules suivants sont requis:", 
                font=('Arial', 10)).pack(pady=10)
        
        missing_text = "\n".join(f"• {module}" for module in missing)
        tk.Label(error_window, text=missing_text, font=('Courier', 10), 
                fg='red', justify=tk.LEFT).pack(pady=10)
        
        tk.Label(error_window, text="Installez-les avec:", 
                font=('Arial', 10, 'bold')).pack(pady=(20, 5))
        
        tk.Label(error_window, text="pip install -r requirements.txt", 
                font=('Courier', 10), bg='#f0f0f0', relief=tk.SUNKEN).pack(pady=5)
        
        tk.Button(error_window, text="Fermer", command=error_window.destroy,
                 bg='#e74c3c', fg='white', padx=20, pady=8).pack(pady=20)
        
        error_window.mainloop()
        return
    
    # Lancement de l'écran d'accueil
    try:
        show_welcome_screen()
    except Exception as e:
        print(f"Erreur fatale: {e}")
        messagebox.showerror("Erreur Fatale", f"Une erreur fatale s'est produite:\n{e}")

if __name__ == "__main__":
    main()
