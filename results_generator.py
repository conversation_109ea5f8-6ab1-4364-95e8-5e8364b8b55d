"""
Module de génération des résultats
"""

import pandas as pd
import json
import logging
from datetime import datetime
from typing import List, Dict, Optional
import os

class ResultsGenerator:
    """Classe pour générer les fichiers de résultats"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.results = []
    
    def add_student_result(self, student_info: Dict, correction_results: List[Dict], total_score: float, max_score: float):
        """
        Ajoute le résultat d'un étudiant
        
        Args:
            student_info: Informations de l'étudiant
            correction_results: Résultats détaillés de correction
            total_score: Score total obtenu
            max_score: Score maximum possible
        """
        percentage = (total_score / max_score * 100) if max_score > 0 else 0
        
        result = {
            'id_etudiant': student_info.get('id', ''),
            'nom': student_info.get('nom', ''),
            'prenom': student_info.get('prenom', ''),
            'nom_complet': student_info.get('nom_complet', ''),
            'classe': student_info.get('classe', ''),
            'score_total': total_score,
            'score_maximum': max_score,
            'pourcentage': round(percentage, 2),
            'note_sur_20': round((total_score / max_score * 20), 2) if max_score > 0 else 0,
            'mention': self._get_mention(percentage),
            'details_correction': correction_results,
            'timestamp': datetime.now().isoformat()
        }
        
        self.results.append(result)
        self.logger.info(f"Résultat ajouté pour {student_info.get('nom_complet', 'Inconnu')}: {total_score}/{max_score}")
    
    def _get_mention(self, percentage: float) -> str:
        """
        Détermine la mention selon le pourcentage
        
        Args:
            percentage: Pourcentage obtenu
            
        Returns:
            Mention correspondante
        """
        if percentage >= 90:
            return "Excellent"
        elif percentage >= 80:
            return "Très Bien"
        elif percentage >= 70:
            return "Bien"
        elif percentage >= 60:
            return "Assez Bien"
        elif percentage >= 50:
            return "Passable"
        else:
            return "Insuffisant"
    
    def generate_excel_report(self, output_path: str) -> bool:
        """
        Génère un rapport Excel avec les résultats
        
        Args:
            output_path: Chemin de sortie du fichier Excel
            
        Returns:
            True si succès, False sinon
        """
        try:
            if not self.results:
                self.logger.warning("Aucun résultat à exporter")
                return False
            
            # Préparer les données pour Excel
            excel_data = []
            for result in self.results:
                row = {
                    'ID Étudiant': result['id_etudiant'],
                    'Nom': result['nom'],
                    'Prénom': result['prenom'],
                    'Classe': result['classe'],
                    'Score Total': result['score_total'],
                    'Score Maximum': result['score_maximum'],
                    'Pourcentage': f"{result['pourcentage']}%",
                    'Note /20': result['note_sur_20'],
                    'Mention': result['mention'],
                    'Date Correction': result['timestamp'][:10]
                }
                
                # Ajouter les détails par question
                for i, detail in enumerate(result['details_correction'], 1):
                    row[f'Q{i} - Score'] = detail.get('note', 0)
                    row[f'Q{i} - Commentaire'] = detail.get('commentaire', '')
                
                excel_data.append(row)
            
            # Créer le DataFrame et sauvegarder
            df = pd.DataFrame(excel_data)
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # Feuille principale avec résumé
                df.to_excel(writer, sheet_name='Résultats', index=False)
                
                # Feuille avec statistiques
                self._add_statistics_sheet(writer, df)
                
                # Feuille avec détails complets
                self._add_detailed_sheet(writer)
            
            self.logger.info(f"Rapport Excel généré: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport Excel: {e}")
            return False
    
    def _add_statistics_sheet(self, writer, df: pd.DataFrame):
        """Ajoute une feuille avec les statistiques"""
        try:
            stats_data = {
                'Statistique': [
                    'Nombre d\'étudiants',
                    'Score moyen',
                    'Score médian',
                    'Score minimum',
                    'Score maximum',
                    'Écart-type',
                    'Taux de réussite (≥50%)',
                    'Note moyenne /20'
                ],
                'Valeur': [
                    len(df),
                    round(df['Score Total'].mean(), 2) if len(df) > 0 else 0,
                    round(df['Score Total'].median(), 2) if len(df) > 0 else 0,
                    df['Score Total'].min() if len(df) > 0 else 0,
                    df['Score Total'].max() if len(df) > 0 else 0,
                    round(df['Score Total'].std(), 2) if len(df) > 0 else 0,
                    f"{len(df[df['Pourcentage'].str.rstrip('%').astype(float) >= 50]) / len(df) * 100:.1f}%" if len(df) > 0 else "0%",
                    round(df['Note /20'].mean(), 2) if len(df) > 0 else 0
                ]
            }
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='Statistiques', index=False)
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création des statistiques: {e}")
    
    def _add_detailed_sheet(self, writer):
        """Ajoute une feuille avec les détails complets"""
        try:
            detailed_data = []
            
            for result in self.results:
                base_info = {
                    'ID': result['id_etudiant'],
                    'Nom Complet': result['nom_complet'],
                    'Score Total': result['score_total'],
                    'Note /20': result['note_sur_20']
                }
                
                for i, detail in enumerate(result['details_correction'], 1):
                    row = base_info.copy()
                    row.update({
                        'Question': f"Question {i}",
                        'Score Question': detail.get('note', 0),
                        'Commentaire': detail.get('commentaire', ''),
                        'Points Forts': detail.get('points_forts', ''),
                        'Points Faibles': detail.get('points_faibles', '')
                    })
                    detailed_data.append(row)
            
            if detailed_data:
                detailed_df = pd.DataFrame(detailed_data)
                detailed_df.to_excel(writer, sheet_name='Détails', index=False)
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la création des détails: {e}")
    
    def generate_json_report(self, output_path: str) -> bool:
        """
        Génère un rapport JSON avec tous les détails
        
        Args:
            output_path: Chemin de sortie du fichier JSON
            
        Returns:
            True si succès, False sinon
        """
        try:
            report = {
                'metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'total_students': len(self.results),
                    'version': '1.0'
                },
                'summary': self._generate_summary(),
                'results': self.results
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Rapport JSON généré: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport JSON: {e}")
            return False
    
    def _generate_summary(self) -> Dict:
        """Génère un résumé des résultats"""
        if not self.results:
            return {}
        
        scores = [r['score_total'] for r in self.results]
        percentages = [r['pourcentage'] for r in self.results]
        
        return {
            'total_students': len(self.results),
            'average_score': round(sum(scores) / len(scores), 2),
            'average_percentage': round(sum(percentages) / len(percentages), 2),
            'min_score': min(scores),
            'max_score': max(scores),
            'pass_rate': len([p for p in percentages if p >= 50]) / len(percentages) * 100,
            'grade_distribution': self._get_grade_distribution()
        }
    
    def _get_grade_distribution(self) -> Dict[str, int]:
        """Calcule la distribution des mentions"""
        distribution = {
            'Excellent': 0,
            'Très Bien': 0,
            'Bien': 0,
            'Assez Bien': 0,
            'Passable': 0,
            'Insuffisant': 0
        }
        
        for result in self.results:
            mention = result['mention']
            if mention in distribution:
                distribution[mention] += 1
        
        return distribution
    
    def get_results_summary(self) -> str:
        """
        Retourne un résumé textuel des résultats
        
        Returns:
            Résumé formaté en texte
        """
        if not self.results:
            return "Aucun résultat disponible."
        
        summary = self._generate_summary()
        
        text = f"""
=== RÉSUMÉ DES CORRECTIONS ===

Nombre d'étudiants corrigés: {summary['total_students']}
Score moyen: {summary['average_score']} ({summary['average_percentage']:.1f}%)
Score minimum: {summary['min_score']}
Score maximum: {summary['max_score']}
Taux de réussite: {summary['pass_rate']:.1f}%

=== DISTRIBUTION DES MENTIONS ===
"""
        
        for mention, count in summary['grade_distribution'].items():
            percentage = (count / summary['total_students'] * 100) if summary['total_students'] > 0 else 0
            text += f"{mention}: {count} étudiants ({percentage:.1f}%)\n"
        
        return text
