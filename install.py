"""
Script d'installation et de configuration du système de correction automatique
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """Vérifie la version de Python"""
    print("🐍 Vérification de la version Python...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"   ❌ Python {version.major}.{version.minor} détecté")
        print("   ⚠️  Python 3.8+ requis")
        return False
    else:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True

def install_dependencies():
    """Installe les dépendances Python"""
    print("📦 Installation des dépendances...")
    
    try:
        # Mise à jour de pip
        print("   🔄 Mise à jour de pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Installation des dépendances
        print("   📥 Installation des packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("   ✅ Dépendances installées avec succès")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Erreur lors de l'installation: {e}")
        return False
    except FileNotFoundError:
        print("   ❌ Fichier requirements.txt non trouvé")
        return False

def check_files():
    """Vérifie la présence des fichiers nécessaires"""
    print("📁 Vérification des fichiers...")
    
    required_files = [
        "config.py",
        "correction_system.py",
        "pdf_analyzer.py",
        "ai_integration.py",
        "student_manager.py",
        "results_generator.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - MANQUANT")
            missing_files.append(file)
    
    if missing_files:
        print(f"   ⚠️  Fichiers manquants: {', '.join(missing_files)}")
        return False
    else:
        print("   ✅ Tous les fichiers système présents")
        return True

def check_data_files():
    """Vérifie les fichiers de données"""
    print("📄 Vérification des fichiers de données...")
    
    data_files = {
        "eleves.txt": "Liste des élèves (✅ présent)",
        "prototype.pdf": "Prototype de correction (❓ à fournir)",
        "examen.pdf": "Fichier d'examen (❓ à fournir)"
    }
    
    for file, description in data_files.items():
        if os.path.exists(file):
            print(f"   ✅ {file} - {description}")
        else:
            print(f"   ⚠️  {file} - {description}")
    
    return True

def test_ai_connection():
    """Teste la connexion à l'IA"""
    print("🤖 Test de la connexion IA...")
    
    try:
        from ai_integration import AICorrector
        
        corrector = AICorrector()
        if corrector.test_connection():
            print("   ✅ Connexion IA réussie")
            return True
        else:
            print("   ❌ Échec de connexion IA")
            print("   💡 Vérifiez votre connexion internet et la clé API")
            return False
            
    except ImportError as e:
        print(f"   ❌ Erreur d'import: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def create_directories():
    """Crée les répertoires nécessaires"""
    print("📂 Création des répertoires...")
    
    directories = [
        "logs",
        "results",
        "temp"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"   ✅ {directory}/")
        except Exception as e:
            print(f"   ❌ Erreur création {directory}/: {e}")

def show_next_steps():
    """Affiche les prochaines étapes"""
    print("\n" + "=" * 60)
    print("🎯 PROCHAINES ÉTAPES")
    print("=" * 60)
    
    steps = [
        "1. 📄 Placez votre fichier 'prototype.pdf' dans ce répertoire",
        "2. 📄 Placez votre fichier 'examen.pdf' dans ce répertoire", 
        "3. ✏️  Modifiez 'eleves.txt' avec votre liste d'élèves",
        "4. 🧪 Lancez les tests: python test_system.py",
        "5. 🚀 Lancez la correction: python correction_system.py"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print("\n📚 Documentation complète dans README.md")

def main():
    """Fonction principale d'installation"""
    print("=" * 60)
    print("🛠️  INSTALLATION DU SYSTÈME DE CORRECTION AUTOMATIQUE")
    print("=" * 60)
    print(f"💻 Système: {platform.system()} {platform.release()}")
    print(f"🐍 Python: {sys.version}")
    print()
    
    # Étapes d'installation
    steps = [
        ("Vérification Python", check_python_version),
        ("Vérification fichiers", check_files),
        ("Installation dépendances", install_dependencies),
        ("Création répertoires", create_directories),
        ("Vérification données", check_data_files),
        ("Test connexion IA", test_ai_connection)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"🔄 {step_name}...")
        try:
            if step_func():
                success_count += 1
                print(f"   ✅ {step_name} - OK")
            else:
                print(f"   ⚠️  {step_name} - ATTENTION")
        except Exception as e:
            print(f"   ❌ {step_name} - ERREUR: {e}")
        print()
    
    # Résumé
    print("=" * 60)
    print("📊 RÉSUMÉ DE L'INSTALLATION")
    print("=" * 60)
    
    if success_count >= 4:  # Les 4 premières étapes sont critiques
        print("🎉 Installation réussie!")
        print("✅ Le système est prêt à être utilisé")
        
        if success_count == len(steps):
            print("🌟 Toutes les vérifications sont passées")
        else:
            print("⚠️  Certaines vérifications ont échoué mais le système devrait fonctionner")
    else:
        print("❌ Installation incomplète")
        print("🔧 Veuillez résoudre les erreurs avant de continuer")
    
    show_next_steps()

if __name__ == "__main__":
    main()
