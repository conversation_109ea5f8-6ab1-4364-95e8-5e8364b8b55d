"""
Interface graphique principale pour le système de correction automatique
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from datetime import datetime
import json

# Import des modules du système
try:
    from correction_system import AutomaticCorrectionSystem
    from config import *
    from test_system import main as run_tests
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

class CorrectionGUI:
    """Interface graphique pour le système de correction"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Système de Correction Automatique - IA Llama 3.3")
        self.root.geometry("900x700")
        
        # Variables
        self.prototype_path = tk.StringVar(value=PROTOTYPE_PDF_PATH)
        self.students_path = tk.StringVar(value=STUDENTS_FILE_PATH)
        self.exam_path = tk.StringVar(value=EXAM_PDF_PATH)
        self.results_path = tk.StringVar(value=RESULTS_FILE_PATH)
        
        self.correction_system = None
        self.is_running = False
        
        self.setup_ui()
        self.check_initial_files()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Titre
        title_label = ttk.Label(main_frame, text="🤖 Système de Correction Automatique", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Section fichiers
        self.create_files_section(main_frame, 1)
        
        # Section configuration
        self.create_config_section(main_frame, 6)
        
        # Section contrôles
        self.create_controls_section(main_frame, 9)
        
        # Section logs
        self.create_logs_section(main_frame, 12)
        
        # Barre de statut
        self.create_status_bar(main_frame, 15)
    
    def create_files_section(self, parent, start_row):
        """Crée la section de sélection des fichiers"""
        # Titre section
        files_label = ttk.Label(parent, text="📁 Fichiers", font=('Arial', 12, 'bold'))
        files_label.grid(row=start_row, column=0, columnspan=3, sticky=tk.W, pady=(10, 5))
        
        # Prototype PDF
        ttk.Label(parent, text="Prototype PDF:").grid(row=start_row+1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(parent, textvariable=self.prototype_path, width=50).grid(row=start_row+1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        ttk.Button(parent, text="Parcourir", command=lambda: self.browse_file(self.prototype_path, "PDF")).grid(row=start_row+1, column=2, pady=2)
        
        # Fichier élèves
        ttk.Label(parent, text="Fichier élèves:").grid(row=start_row+2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(parent, textvariable=self.students_path, width=50).grid(row=start_row+2, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        ttk.Button(parent, text="Parcourir", command=lambda: self.browse_file(self.students_path, "TXT")).grid(row=start_row+2, column=2, pady=2)
        
        # Examen PDF
        ttk.Label(parent, text="Examen PDF:").grid(row=start_row+3, column=0, sticky=tk.W, pady=2)
        ttk.Entry(parent, textvariable=self.exam_path, width=50).grid(row=start_row+3, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        ttk.Button(parent, text="Parcourir", command=lambda: self.browse_file(self.exam_path, "PDF")).grid(row=start_row+3, column=2, pady=2)
        
        # Fichier résultats
        ttk.Label(parent, text="Résultats:").grid(row=start_row+4, column=0, sticky=tk.W, pady=2)
        ttk.Entry(parent, textvariable=self.results_path, width=50).grid(row=start_row+4, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        ttk.Button(parent, text="Parcourir", command=lambda: self.browse_save_file(self.results_path)).grid(row=start_row+4, column=2, pady=2)
    
    def create_config_section(self, parent, start_row):
        """Crée la section de configuration"""
        # Titre section
        config_label = ttk.Label(parent, text="⚙️ Configuration", font=('Arial', 12, 'bold'))
        config_label.grid(row=start_row, column=0, columnspan=3, sticky=tk.W, pady=(20, 5))
        
        # Frame pour la configuration
        config_frame = ttk.LabelFrame(parent, text="Paramètres IA", padding="10")
        config_frame.grid(row=start_row+1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        config_frame.columnconfigure(1, weight=1)
        
        # Modèle IA
        ttk.Label(config_frame, text="Modèle:").grid(row=0, column=0, sticky=tk.W, pady=2)
        model_label = ttk.Label(config_frame, text=MODEL_NAME, foreground="blue")
        model_label.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Note maximale
        ttk.Label(config_frame, text="Note max:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.max_score_var = tk.StringVar(value=str(MAX_SCORE))
        ttk.Entry(config_frame, textvariable=self.max_score_var, width=10).grid(row=1, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # Statut connexion
        ttk.Label(config_frame, text="Connexion IA:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.connection_status = ttk.Label(config_frame, text="Non testée", foreground="orange")
        self.connection_status.grid(row=2, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        ttk.Button(config_frame, text="Tester", command=self.test_ai_connection).grid(row=2, column=2, pady=2)
    
    def create_controls_section(self, parent, start_row):
        """Crée la section des contrôles"""
        # Titre section
        controls_label = ttk.Label(parent, text="🎮 Contrôles", font=('Arial', 12, 'bold'))
        controls_label.grid(row=start_row, column=0, columnspan=3, sticky=tk.W, pady=(20, 5))
        
        # Frame pour les boutons
        controls_frame = ttk.Frame(parent)
        controls_frame.grid(row=start_row+1, column=0, columnspan=3, pady=5)
        
        # Boutons
        self.test_button = ttk.Button(controls_frame, text="🧪 Tester le système", command=self.run_tests)
        self.test_button.grid(row=0, column=0, padx=5)
        
        self.start_button = ttk.Button(controls_frame, text="🚀 Démarrer la correction", command=self.start_correction)
        self.start_button.grid(row=0, column=1, padx=5)
        
        self.stop_button = ttk.Button(controls_frame, text="⏹️ Arrêter", command=self.stop_correction, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=2, padx=5)
        
        self.results_button = ttk.Button(controls_frame, text="📊 Voir résultats", command=self.view_results, state=tk.DISABLED)
        self.results_button.grid(row=0, column=3, padx=5)

        self.config_button = ttk.Button(controls_frame, text="⚙️ Configuration", command=self.open_config)
        self.config_button.grid(row=0, column=4, padx=5)
        
        # Barre de progression
        self.progress = ttk.Progressbar(parent, mode='indeterminate')
        self.progress.grid(row=start_row+2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
    
    def create_logs_section(self, parent, start_row):
        """Crée la section des logs"""
        # Titre section
        logs_label = ttk.Label(parent, text="📋 Journal d'activité", font=('Arial', 12, 'bold'))
        logs_label.grid(row=start_row, column=0, columnspan=3, sticky=tk.W, pady=(20, 5))
        
        # Zone de texte pour les logs
        self.log_text = scrolledtext.ScrolledText(parent, height=15, width=80)
        self.log_text.grid(row=start_row+1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # Configuration du redimensionnement
        parent.rowconfigure(start_row+1, weight=1)
        
        # Bouton pour effacer les logs
        ttk.Button(parent, text="🗑️ Effacer logs", command=self.clear_logs).grid(row=start_row+2, column=0, sticky=tk.W, pady=5)
    
    def create_status_bar(self, parent, start_row):
        """Crée la barre de statut"""
        self.status_var = tk.StringVar(value="Prêt")
        status_bar = ttk.Label(parent, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=start_row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def browse_file(self, var, file_type):
        """Ouvre un dialogue pour sélectionner un fichier"""
        if file_type == "PDF":
            filetypes = [("Fichiers PDF", "*.pdf"), ("Tous les fichiers", "*.*")]
        elif file_type == "TXT":
            filetypes = [("Fichiers texte", "*.txt"), ("Tous les fichiers", "*.*")]
        else:
            filetypes = [("Tous les fichiers", "*.*")]
        
        filename = filedialog.askopenfilename(
            title=f"Sélectionner un fichier {file_type}",
            filetypes=filetypes
        )
        
        if filename:
            var.set(filename)
            self.log(f"Fichier sélectionné: {os.path.basename(filename)}")
    
    def browse_save_file(self, var):
        """Ouvre un dialogue pour sauvegarder un fichier"""
        filename = filedialog.asksaveasfilename(
            title="Enregistrer les résultats",
            defaultextension=".xlsx",
            filetypes=[("Fichiers Excel", "*.xlsx"), ("Tous les fichiers", "*.*")]
        )
        
        if filename:
            var.set(filename)
            self.log(f"Fichier de sortie: {os.path.basename(filename)}")
    
    def check_initial_files(self):
        """Vérifie la présence des fichiers au démarrage"""
        files_status = []
        
        files_to_check = [
            (self.prototype_path.get(), "Prototype PDF"),
            (self.students_path.get(), "Fichier élèves"),
            (self.exam_path.get(), "Examen PDF")
        ]
        
        for file_path, description in files_to_check:
            if os.path.exists(file_path):
                files_status.append(f"✅ {description}")
            else:
                files_status.append(f"❌ {description}")
        
        self.log("Vérification des fichiers:")
        for status in files_status:
            self.log(f"  {status}")
    
    def test_ai_connection(self):
        """Teste la connexion à l'IA"""
        self.log("Test de connexion à l'IA...")
        self.connection_status.config(text="Test en cours...", foreground="orange")
        
        def test_thread():
            try:
                from ai_integration import AICorrector
                corrector = AICorrector()
                
                if corrector.test_connection():
                    self.root.after(0, lambda: self.connection_status.config(text="✅ Connecté", foreground="green"))
                    self.log("✅ Connexion IA réussie")
                else:
                    self.root.after(0, lambda: self.connection_status.config(text="❌ Échec", foreground="red"))
                    self.log("❌ Échec de connexion IA")
            except Exception as e:
                self.root.after(0, lambda: self.connection_status.config(text="❌ Erreur", foreground="red"))
                self.log(f"❌ Erreur de connexion: {e}")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def run_tests(self):
        """Lance les tests du système"""
        self.log("🧪 Lancement des tests du système...")
        self.test_button.config(state=tk.DISABLED)
        
        def test_thread():
            try:
                # Rediriger la sortie vers les logs
                import io
                import contextlib
                
                f = io.StringIO()
                with contextlib.redirect_stdout(f):
                    run_tests()
                
                output = f.getvalue()
                self.root.after(0, lambda: self.log(output))
                self.root.after(0, lambda: self.test_button.config(state=tk.NORMAL))
                
            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ Erreur lors des tests: {e}"))
                self.root.after(0, lambda: self.test_button.config(state=tk.NORMAL))
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def start_correction(self):
        """Démarre le processus de correction"""
        # Vérification des fichiers
        if not self.validate_files():
            return
        
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress.start()
        
        self.log("🚀 Démarrage de la correction automatique...")
        self.status_var.set("Correction en cours...")
        
        def correction_thread():
            try:
                # Mise à jour de la configuration
                global MAX_SCORE
                MAX_SCORE = float(self.max_score_var.get())
                
                # Mise à jour des chemins
                global PROTOTYPE_PDF_PATH, STUDENTS_FILE_PATH, EXAM_PDF_PATH, RESULTS_FILE_PATH
                PROTOTYPE_PDF_PATH = self.prototype_path.get()
                STUDENTS_FILE_PATH = self.students_path.get()
                EXAM_PDF_PATH = self.exam_path.get()
                RESULTS_FILE_PATH = self.results_path.get()
                
                # Lancement de la correction
                self.correction_system = AutomaticCorrectionSystem()
                success = self.correction_system.run_full_correction()
                
                if success:
                    self.root.after(0, self.correction_completed)
                else:
                    self.root.after(0, self.correction_failed)
                    
            except Exception as e:
                self.root.after(0, lambda: self.correction_error(str(e)))
        
        threading.Thread(target=correction_thread, daemon=True).start()
    
    def stop_correction(self):
        """Arrête le processus de correction"""
        self.is_running = False
        self.progress.stop()
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_var.set("Arrêté par l'utilisateur")
        self.log("⏹️ Correction arrêtée par l'utilisateur")
    
    def correction_completed(self):
        """Appelé quand la correction est terminée avec succès"""
        self.is_running = False
        self.progress.stop()
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.results_button.config(state=tk.NORMAL)
        self.status_var.set("Correction terminée avec succès")
        
        self.log("🎉 Correction terminée avec succès!")
        self.log(f"📊 Résultats disponibles: {self.results_path.get()}")
        
        messagebox.showinfo("Succès", "La correction automatique est terminée!\n\nConsultez les résultats dans le fichier généré.")
    
    def correction_failed(self):
        """Appelé quand la correction échoue"""
        self.is_running = False
        self.progress.stop()
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_var.set("Erreur lors de la correction")
        
        self.log("❌ Échec de la correction")
        messagebox.showerror("Erreur", "La correction a échoué.\n\nConsultez les logs pour plus de détails.")
    
    def correction_error(self, error_msg):
        """Appelé en cas d'erreur durant la correction"""
        self.is_running = False
        self.progress.stop()
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_var.set("Erreur fatale")
        
        self.log(f"💥 Erreur fatale: {error_msg}")
        messagebox.showerror("Erreur fatale", f"Une erreur fatale s'est produite:\n\n{error_msg}")
    
    def validate_files(self):
        """Valide la présence des fichiers requis"""
        files_to_check = [
            (self.prototype_path.get(), "Prototype PDF"),
            (self.students_path.get(), "Fichier élèves"),
            (self.exam_path.get(), "Examen PDF")
        ]
        
        missing_files = []
        for file_path, description in files_to_check:
            if not os.path.exists(file_path):
                missing_files.append(description)
        
        if missing_files:
            messagebox.showerror("Fichiers manquants", 
                               f"Les fichiers suivants sont manquants:\n\n" + 
                               "\n".join(f"• {f}" for f in missing_files))
            return False
        
        return True
    
    def view_results(self):
        """Ouvre le visualiseur de résultats"""
        results_file = self.results_path.get()

        if os.path.exists(results_file):
            try:
                from gui_results import show_results_viewer
                show_results_viewer(self.root, results_file)
                self.log(f"📊 Visualiseur de résultats ouvert: {os.path.basename(results_file)}")
            except ImportError:
                # Fallback vers l'ouverture système
                try:
                    os.startfile(results_file)  # Windows
                except AttributeError:
                    try:
                        os.system(f"open {results_file}")  # macOS
                    except:
                        os.system(f"xdg-open {results_file}")  # Linux
                self.log(f"📊 Ouverture du fichier: {os.path.basename(results_file)}")
        else:
            messagebox.showwarning("Fichier introuvable", "Le fichier de résultats n'existe pas encore.")
    
    def log(self, message):
        """Ajoute un message aux logs"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_logs(self):
        """Efface les logs"""
        self.log_text.delete(1.0, tk.END)
        self.log("📋 Logs effacés")

    def open_config(self):
        """Ouvre la fenêtre de configuration"""
        try:
            from gui_config import show_config_window
            show_config_window(self.root)
        except ImportError:
            messagebox.showerror("Erreur", "Module de configuration non disponible")

def main():
    """Fonction principale"""
    root = tk.Tk()
    app = CorrectionGUI(root)
    
    # Gestion de la fermeture
    def on_closing():
        if app.is_running:
            if messagebox.askokcancel("Quitter", "Une correction est en cours. Voulez-vous vraiment quitter?"):
                app.stop_correction()
                root.destroy()
        else:
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # Centrer la fenêtre
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
