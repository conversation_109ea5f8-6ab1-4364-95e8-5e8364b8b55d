"""
Système principal de correction automatique de tests
"""

import logging
import os
import sys
from typing import Dict, List, Optional
from datetime import datetime

from pdf_analyzer import PDFAnalyzer
from ai_integration import AICorrector
from student_manager import StudentManager
from results_generator import ResultsGenerator
from config import *

class AutomaticCorrectionSystem:
    """Système principal de correction automatique"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialisation des modules
        self.pdf_analyzer = PDFAnalyzer()
        self.ai_corrector = AICorrector()
        self.student_manager = StudentManager()
        self.results_generator = ResultsGenerator()
        
        # Variables d'état
        self.prototype_data = None
        self.exam_data = None
        self.students_data = []
        
        self.logger.info("Système de correction automatique initialisé")
    
    def setup_logging(self):
        """Configure le système de logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('correction_system.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def run_full_correction(self) -> bool:
        """
        Lance le processus complet de correction
        
        Returns:
            True si succès, False sinon
        """
        try:
            self.logger.info("=== DÉBUT DU PROCESSUS DE CORRECTION ===")
            
            # Étape 1: Vérification des fichiers
            if not self._check_required_files():
                return False
            
            # Étape 2: Test de connexion IA
            if not self._test_ai_connection():
                return False
            
            # Étape 3: Analyse du prototype
            if not self._analyze_prototype():
                return False
            
            # Étape 4: Chargement des élèves
            if not self._load_students():
                return False
            
            # Étape 5: Analyse de l'examen
            if not self._analyze_exam():
                return False
            
            # Étape 6: Correction des copies
            if not self._correct_all_students():
                return False
            
            # Étape 7: Génération des résultats
            if not self._generate_results():
                return False
            
            self.logger.info("=== PROCESSUS DE CORRECTION TERMINÉ AVEC SUCCÈS ===")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur fatale dans le processus de correction: {e}")
            return False
    
    def _check_required_files(self) -> bool:
        """Vérifie la présence des fichiers requis"""
        required_files = [
            (PROTOTYPE_PDF_PATH, "Prototype PDF"),
            (STUDENTS_FILE_PATH, "Fichier élèves"),
            (EXAM_PDF_PATH, "Examen PDF")
        ]
        
        missing_files = []
        for file_path, description in required_files:
            if not os.path.exists(file_path):
                missing_files.append(f"{description} ({file_path})")
        
        if missing_files:
            self.logger.error(f"Fichiers manquants: {', '.join(missing_files)}")
            return False
        
        self.logger.info("Tous les fichiers requis sont présents")
        return True
    
    def _test_ai_connection(self) -> bool:
        """Teste la connexion à l'IA"""
        self.logger.info("Test de connexion à l'IA...")
        
        if self.ai_corrector.test_connection():
            self.logger.info("Connexion à l'IA réussie")
            return True
        else:
            self.logger.error("Impossible de se connecter à l'IA")
            return False
    
    def _analyze_prototype(self) -> bool:
        """Analyse le prototype de correction"""
        self.logger.info("Analyse du prototype de correction...")
        
        try:
            # Extraction du texte du prototype
            prototype_text = self.pdf_analyzer.extract_text(PROTOTYPE_PDF_PATH)
            if not prototype_text:
                self.logger.error("Impossible d'extraire le texte du prototype")
                return False
            
            # Analyse avec l'IA
            self.prototype_data = self.ai_corrector.analyze_prototype(prototype_text)
            
            if not self.prototype_data:
                self.logger.error("Échec de l'analyse du prototype par l'IA")
                return False
            
            self.logger.info("Prototype analysé avec succès")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'analyse du prototype: {e}")
            return False
    
    def _load_students(self) -> bool:
        """Charge la liste des élèves"""
        self.logger.info("Chargement de la liste des élèves...")
        
        try:
            self.students_data = self.student_manager.load_students_from_file(STUDENTS_FILE_PATH)
            
            if not self.students_data:
                self.logger.error("Aucun élève chargé")
                return False
            
            # Validation des données
            validation = self.student_manager.validate_students_data()
            self.logger.info(f"Élèves chargés: {validation['valid']}/{validation['total']} valides")
            
            if validation['duplicates']:
                self.logger.warning(f"Doublons détectés: {validation['duplicates']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des élèves: {e}")
            return False
    
    def _analyze_exam(self) -> bool:
        """Analyse le fichier d'examen"""
        self.logger.info("Analyse du fichier d'examen...")
        
        try:
            # Extraction du texte de l'examen
            exam_text = self.pdf_analyzer.extract_text(EXAM_PDF_PATH)
            if not exam_text:
                self.logger.error("Impossible d'extraire le texte de l'examen")
                return False
            
            self.exam_data = {
                "content": exam_text,
                "structure": self.pdf_analyzer._analyze_exam_structure(exam_text),
                "questions": self.pdf_analyzer._extract_questions(exam_text)
            }
            
            self.logger.info(f"Examen analysé: {len(self.exam_data['questions'])} questions détectées")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'analyse de l'examen: {e}")
            return False
    
    def _correct_all_students(self) -> bool:
        """Corrige les copies de tous les élèves"""
        self.logger.info("Début de la correction des copies...")
        
        try:
            total_students = len(self.students_data)
            
            for i, student in enumerate(self.students_data, 1):
                self.logger.info(f"Correction de {student['nom_complet']} ({i}/{total_students})")
                
                if not self._correct_student(student):
                    self.logger.warning(f"Échec de la correction pour {student['nom_complet']}")
                    continue
            
            self.logger.info("Correction de toutes les copies terminée")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la correction des copies: {e}")
            return False
    
    def _correct_student(self, student: Dict) -> bool:
        """
        Corrige la copie d'un élève
        
        Args:
            student: Informations de l'élève
            
        Returns:
            True si succès, False sinon
        """
        try:
            # Pour cette version, on simule les réponses de l'élève
            # Dans une version complète, il faudrait extraire les réponses de l'élève
            # depuis son fichier de copie individuel
            
            student_answers = self._simulate_student_answers(student)
            correction_results = []
            total_score = 0
            
            # Correction question par question
            if "questions" in self.prototype_data and "reponses_attendues" in self.prototype_data:
                questions = self.prototype_data["questions"]
                expected_answers = self.prototype_data["reponses_attendues"]
                
                for i, (question, expected) in enumerate(zip(questions, expected_answers)):
                    student_answer = student_answers.get(f"question_{i+1}", "")
                    
                    result = self.ai_corrector.correct_student_answer(
                        question=question,
                        student_answer=student_answer,
                        expected_answer=expected,
                        max_points=MAX_SCORE / len(questions)
                    )
                    
                    correction_results.append(result)
                    total_score += result.get("note", 0)
            
            # Ajout du résultat
            self.results_generator.add_student_result(
                student_info=student,
                correction_results=correction_results,
                total_score=total_score,
                max_score=MAX_SCORE
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la correction de {student.get('nom_complet', 'Inconnu')}: {e}")
            return False
    
    def _simulate_student_answers(self, student: Dict) -> Dict[str, str]:
        """
        Simule les réponses d'un élève (à remplacer par l'extraction réelle)
        
        Args:
            student: Informations de l'élève
            
        Returns:
            Dictionnaire des réponses simulées
        """
        # Cette fonction devrait être remplacée par l'extraction réelle
        # des réponses depuis le fichier de copie de l'élève
        
        return {
            "question_1": f"Réponse simulée de {student['prenom']} à la question 1",
            "question_2": f"Réponse simulée de {student['prenom']} à la question 2",
            "question_3": f"Réponse simulée de {student['prenom']} à la question 3"
        }
    
    def _generate_results(self) -> bool:
        """Génère les fichiers de résultats"""
        self.logger.info("Génération des fichiers de résultats...")
        
        try:
            # Génération du rapport Excel
            excel_success = self.results_generator.generate_excel_report(RESULTS_FILE_PATH)
            
            # Génération du rapport JSON
            json_path = RESULTS_FILE_PATH.replace('.xlsx', '.json')
            json_success = self.results_generator.generate_json_report(json_path)
            
            # Affichage du résumé
            summary = self.results_generator.get_results_summary()
            self.logger.info(f"Résumé des résultats:\n{summary}")
            
            if excel_success and json_success:
                self.logger.info("Tous les fichiers de résultats ont été générés avec succès")
                return True
            else:
                self.logger.warning("Certains fichiers de résultats n'ont pas pu être générés")
                return False
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération des résultats: {e}")
            return False

def main():
    """Fonction principale"""
    print("=== SYSTÈME DE CORRECTION AUTOMATIQUE ===")
    print("Initialisation...")
    
    system = AutomaticCorrectionSystem()
    
    if system.run_full_correction():
        print("\n✅ Correction terminée avec succès!")
        print(f"📊 Résultats disponibles dans: {RESULTS_FILE_PATH}")
    else:
        print("\n❌ Erreur lors de la correction")
        print("📋 Consultez le fichier correction_system.log pour plus de détails")

if __name__ == "__main__":
    main()
