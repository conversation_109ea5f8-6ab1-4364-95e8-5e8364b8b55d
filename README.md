# Système de Correction Automatique de Tests

Ce système utilise l'intelligence artificielle (Llama 3.3 8B Instruct) pour corriger automatiquement des tests en analysant un prototype de correction et les copies d'élèves.

## 🚀 Fonctionnalités

- **Analyse de prototype PDF** : Extraction et analyse du corrigé type
- **Gestion des élèves** : Chargement depuis un fichier texte
- **Analyse d'examen PDF** : Extraction des questions et structure
- **Correction automatique** : Utilisation de l'IA pour noter les réponses
- **Génération de résultats** : Rapports Excel et JSON détaillés

## 📋 Prérequis

1. **Python 3.8+**
2. **Clé API OpenRouter** pour Llama 3.3 (déjà configurée)
3. **Fichiers requis** :
   - `prototype.pdf` : Corrigé type de l'examen
   - `eleves.txt` : Liste des élèves
   - `examen.pdf` : Fichier d'examen à corriger

## 🛠️ Installation

1. **Installer les dépendances** :
```bash
pip install -r requirements.txt
```

2. **Préparer les fichiers** :
   - Placez votre `prototype.pdf` dans le répertoire
   - Placez votre `examen.pdf` dans le répertoire
   - Modifiez `eleves.txt` avec votre liste d'élèves

## 📁 Structure des fichiers

### Fichier élèves (`eleves.txt`)
Formats supportés :
```
# Commentaires commencent par #
Nom Prénom
ID:123 Nom Prénom - Classe
Sophie, Martin - Classe A
```

### Fichiers PDF
- **prototype.pdf** : Doit contenir les questions et réponses attendues
- **examen.pdf** : Contient les questions de l'examen

## 🎯 Utilisation

### Interface Graphique (Recommandé)

#### Lancement rapide (Windows)
```bash
lancer_interface.bat
```

#### Lancement universel
```bash
python launch_gui.py
```

#### Fonctionnalités de l'interface :
- **🖱️ Interface intuitive** : Sélection de fichiers par glisser-déposer
- **⚙️ Configuration avancée** : Paramètres IA et personnalisation
- **📊 Visualisation des résultats** : Graphiques et statistiques interactifs
- **🧪 Tests intégrés** : Vérification du système en un clic
- **📋 Logs en temps réel** : Suivi du processus de correction

### Lancement en ligne de commande
```bash
python correction_system.py
```

### Utilisation programmatique
```python
from correction_system import AutomaticCorrectionSystem

system = AutomaticCorrectionSystem()
success = system.run_full_correction()

if success:
    print("Correction terminée avec succès!")
else:
    print("Erreur lors de la correction")
```

## 📊 Résultats

Le système génère :

1. **`resultats.xlsx`** : Rapport Excel avec :
   - Feuille "Résultats" : Notes et mentions
   - Feuille "Statistiques" : Analyses statistiques
   - Feuille "Détails" : Corrections détaillées

2. **`resultats.json`** : Données complètes en JSON

3. **`correction_system.log`** : Journal détaillé du processus

## ⚙️ Configuration

Modifiez `config.py` pour :
- Changer les chemins des fichiers
- Ajuster les paramètres de l'IA
- Modifier le système de notation

```python
# Exemple de configuration
MAX_SCORE = 20  # Note maximale
TEMPERATURE = 0.3  # Créativité de l'IA (0-1)
MODEL_NAME = "meta-llama/llama-3.3-70b-instruct:free"
```

## 🔧 Modules

### Modules principaux
- **`correction_system.py`** : Module principal de correction
- **`pdf_analyzer.py`** : Analyse des fichiers PDF
- **`ai_integration.py`** : Intégration avec l'IA Llama 3.3
- **`student_manager.py`** : Gestion des élèves
- **`results_generator.py`** : Génération des rapports
- **`config.py`** : Configuration du système

### Interface graphique
- **`launch_gui.py`** : Lanceur principal avec écran d'accueil
- **`gui_main.py`** : Interface principale de correction
- **`gui_config.py`** : Interface de configuration avancée
- **`gui_results.py`** : Visualiseur de résultats interactif
- **`lancer_interface.bat`** : Script de lancement Windows

### Utilitaires
- **`install.py`** : Installation et vérification automatique
- **`test_system.py`** : Tests complets du système

## 📝 Exemple de sortie

```
=== RÉSUMÉ DES CORRECTIONS ===

Nombre d'étudiants corrigés: 10
Score moyen: 14.5 (72.5%)
Score minimum: 8.0
Score maximum: 19.0
Taux de réussite: 80.0%

=== DISTRIBUTION DES MENTIONS ===
Excellent: 1 étudiants (10.0%)
Très Bien: 2 étudiants (20.0%)
Bien: 3 étudiants (30.0%)
Assez Bien: 2 étudiants (20.0%)
Passable: 1 étudiants (10.0%)
Insuffisant: 1 étudiants (10.0%)
```

## 🚨 Dépannage

### Erreurs courantes

1. **Fichiers manquants** :
   ```
   Erreur: Fichiers manquants: Prototype PDF (prototype.pdf)
   ```
   → Vérifiez que tous les fichiers requis sont présents

2. **Erreur de connexion IA** :
   ```
   Erreur: Impossible de se connecter à l'IA
   ```
   → Vérifiez votre connexion internet et la clé API

3. **Erreur d'extraction PDF** :
   ```
   Erreur: Impossible d'extraire le texte du PDF
   ```
   → Vérifiez que le PDF n'est pas protégé ou corrompu

### Logs détaillés
Consultez `correction_system.log` pour un diagnostic complet.

## 🔒 Sécurité

- La clé API est stockée dans `config.py`
- Les données ne sont traitées que localement
- Aucune donnée n'est envoyée à des tiers (sauf l'API OpenRouter)

## 🤝 Support

Pour toute question ou problème :
1. Consultez les logs dans `correction_system.log`
2. Vérifiez la configuration dans `config.py`
3. Testez avec des fichiers d'exemple simples

## 📈 Améliorations futures

- Support de formats de fichiers supplémentaires
- Interface graphique
- Correction collaborative
- Intégration avec des LMS
- Support multilingue
