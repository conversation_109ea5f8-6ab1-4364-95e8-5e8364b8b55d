"""
Script de test pour le système de correction automatique
"""

import os
import sys
import logging
from datetime import datetime

# Import des modules du système
from pdf_analyzer import PDFAnalyzer
from ai_integration import AICorrector
from student_manager import StudentManager
from results_generator import ResultsGenerator

def test_student_manager():
    """Test du gestionnaire d'élèves"""
    print("🧪 Test du gestionnaire d'élèves...")
    
    manager = StudentManager()
    
    # Test avec le fichier d'exemple
    if os.path.exists("eleves.txt"):
        students = manager.load_students_from_file("eleves.txt")
        print(f"   ✅ {len(students)} élèves chargés")
        
        # Validation
        validation = manager.validate_students_data()
        print(f"   📊 Validation: {validation['valid']}/{validation['total']} valides")
        
        # Test de recherche
        if students:
            first_student = students[0]
            found = manager.get_student_by_name(first_student['nom'])
            if found:
                print(f"   🔍 Recherche réussie: {found['nom_complet']}")
            else:
                print("   ❌ Erreur de recherche")
    else:
        print("   ⚠️  Fichier eleves.txt non trouvé")
    
    return True

def test_ai_connection():
    """Test de la connexion IA"""
    print("🧪 Test de la connexion IA...")
    
    corrector = AICorrector()
    
    if corrector.test_connection():
        print("   ✅ Connexion IA réussie")
        
        # Test de correction simple
        result = corrector.correct_student_answer(
            question="Quelle est la capitale de la France ?",
            student_answer="Paris",
            expected_answer="Paris est la capitale de la France",
            max_points=5
        )
        
        if result and "note" in result:
            print(f"   📝 Test de correction: {result['note']}/5 points")
            print(f"   💬 Commentaire: {result.get('commentaire', 'N/A')}")
        else:
            print("   ⚠️  Erreur dans le test de correction")
        
        return True
    else:
        print("   ❌ Échec de connexion IA")
        return False

def test_pdf_analyzer():
    """Test de l'analyseur PDF"""
    print("🧪 Test de l'analyseur PDF...")
    
    analyzer = PDFAnalyzer()
    
    # Créer un fichier PDF de test simple si nécessaire
    test_files = ["prototype.pdf", "examen.pdf"]
    found_files = []
    
    for file in test_files:
        if os.path.exists(file):
            found_files.append(file)
            try:
                text = analyzer.extract_text(file)
                if text:
                    print(f"   ✅ {file}: {len(text)} caractères extraits")
                else:
                    print(f"   ⚠️  {file}: Aucun texte extrait")
            except Exception as e:
                print(f"   ❌ {file}: Erreur - {e}")
    
    if not found_files:
        print("   ⚠️  Aucun fichier PDF de test trouvé")
        print("   💡 Créez prototype.pdf et examen.pdf pour tester")
    
    return len(found_files) > 0

def test_results_generator():
    """Test du générateur de résultats"""
    print("🧪 Test du générateur de résultats...")
    
    generator = ResultsGenerator()
    
    # Ajouter des résultats de test
    test_students = [
        {"id": "001", "nom": "Test", "prenom": "Élève1", "nom_complet": "Élève1 Test", "classe": "Test"},
        {"id": "002", "nom": "Test", "prenom": "Élève2", "nom_complet": "Élève2 Test", "classe": "Test"}
    ]
    
    test_results = [
        [{"note": 15, "commentaire": "Bonne réponse"}],
        [{"note": 12, "commentaire": "Réponse correcte mais incomplète"}]
    ]
    
    for i, student in enumerate(test_students):
        generator.add_student_result(
            student_info=student,
            correction_results=test_results[i],
            total_score=test_results[i][0]["note"],
            max_score=20
        )
    
    # Test de génération Excel
    test_excel_path = "test_resultats.xlsx"
    if generator.generate_excel_report(test_excel_path):
        print(f"   ✅ Rapport Excel généré: {test_excel_path}")
        
        # Nettoyage
        try:
            os.remove(test_excel_path)
            print("   🧹 Fichier de test nettoyé")
        except:
            pass
    else:
        print("   ❌ Erreur génération Excel")
    
    # Test de génération JSON
    test_json_path = "test_resultats.json"
    if generator.generate_json_report(test_json_path):
        print(f"   ✅ Rapport JSON généré: {test_json_path}")
        
        # Nettoyage
        try:
            os.remove(test_json_path)
            print("   🧹 Fichier de test nettoyé")
        except:
            pass
    else:
        print("   ❌ Erreur génération JSON")
    
    # Test du résumé
    summary = generator.get_results_summary()
    if summary:
        print("   ✅ Résumé généré avec succès")
    
    return True

def create_sample_files():
    """Crée des fichiers d'exemple pour les tests"""
    print("📁 Création de fichiers d'exemple...")
    
    # Créer un fichier PDF simple (simulation)
    sample_pdf_content = """
    PROTOTYPE DE CORRECTION
    
    Question 1: Quelle est la capitale de la France ?
    Réponse attendue: Paris est la capitale de la France.
    
    Question 2: Combien font 2 + 2 ?
    Réponse attendue: 2 + 2 = 4
    
    Question 3: Qui a écrit "Les Misérables" ?
    Réponse attendue: Victor Hugo a écrit "Les Misérables".
    """
    
    print("   💡 Pour tester complètement le système, créez:")
    print("   - prototype.pdf avec le corrigé type")
    print("   - examen.pdf avec les questions")
    print("   ✅ eleves.txt déjà créé")

def main():
    """Fonction principale de test"""
    print("=" * 50)
    print("🧪 TESTS DU SYSTÈME DE CORRECTION AUTOMATIQUE")
    print("=" * 50)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Configuration du logging pour les tests
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        ("Gestionnaire d'élèves", test_student_manager),
        ("Connexion IA", test_ai_connection),
        ("Analyseur PDF", test_pdf_analyzer),
        ("Générateur de résultats", test_results_generator)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
            print()
        except Exception as e:
            print(f"   ❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
            print()
    
    # Résumé des tests
    print("=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASSÉ" if success else "❌ ÉCHEC"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print()
    print(f"🎯 Résultat global: {passed}/{len(results)} tests réussis")
    
    if passed == len(results):
        print("🎉 Tous les tests sont passés! Le système est prêt.")
    else:
        print("⚠️  Certains tests ont échoué. Vérifiez la configuration.")
    
    print()
    create_sample_files()

if __name__ == "__main__":
    main()
